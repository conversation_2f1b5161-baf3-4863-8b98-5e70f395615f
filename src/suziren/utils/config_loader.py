"""
配置加载工具
"""

import os
import yaml
import json
from typing import Dict, Any


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    file_ext = os.path.splitext(config_path)[1].lower()
    
    with open(config_path, 'r', encoding='utf-8') as f:
        if file_ext in ['.yaml', '.yml']:
            return yaml.safe_load(f)
        elif file_ext == '.json':
            return json.load(f)
        else:
            raise ValueError(f"不支持的配置文件格式: {file_ext}")


def save_config(config: Dict[str, Any], config_path: str):
    """
    保存配置文件
    
    Args:
        config: 配置字典
        config_path: 配置文件路径
    """
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    file_ext = os.path.splitext(config_path)[1].lower()
    
    with open(config_path, 'w', encoding='utf-8') as f:
        if file_ext in ['.yaml', '.yml']:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        elif file_ext == '.json':
            json.dump(config, f, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的配置文件格式: {file_ext}")
