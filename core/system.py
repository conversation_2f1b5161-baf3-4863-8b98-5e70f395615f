"""
音频交互系统核心
"""

import asyncio
import time
import logging
from typing import Dict, Optional, Callable, Any
from models import KWSModel, ASRModel, DenoiseModel, TTSModel
from services import VoiceInteractionService, AudioProcessor


class AudioInteractionSystem:
    """音频交互系统核心处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化音频交互系统
        
        Args:
            config: 系统配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 模型实例
        self.kws_model: Optional[KWSModel] = None
        self.asr_model: Optional[ASRModel] = None
        self.denoise_model: Optional[DenoiseModel] = None
        self.tts_model: Optional[TTSModel] = None
        
        # 服务实例
        self.voice_service: Optional[VoiceInteractionService] = None
        self.audio_processor: Optional[AudioProcessor] = None
        
        # 系统状态
        self.is_initialized = False
        self.is_listening = False
        
        # 回调函数
        self.on_wake_word_detected: Optional[Callable] = None
        self.on_speech_recognized: Optional[Callable] = None
        self.on_response_generated: Optional[Callable] = None
    
    async def initialize(self):
        """异步初始化系统"""
        if self.is_initialized:
            return
        
        self.logger.info("开始初始化音频交互系统...")
        start_time = time.time()
        
        try:
            # 初始化音频处理器
            self.audio_processor = AudioProcessor(
                sample_rate=self.config.get('audio', {}).get('sample_rate', 16000)
            )
            
            # 加载模型
            await self._load_models()
            
            # 初始化语音交互服务
            self.voice_service = VoiceInteractionService(
                kws_model=self.kws_model,
                asr_model=self.asr_model,
                denoise_model=self.denoise_model,
                audio_processor=self.audio_processor
            )
            
            # 设置回调
            self._setup_callbacks()
            
            self.is_initialized = True
            init_time = time.time() - start_time
            self.logger.info(f"✓ 音频交互系统初始化完成，耗时: {init_time:.2f}秒")
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}")
            raise
    
    async def _load_models(self):
        """加载所有模型"""
        model_configs = self.config.get('models', {})
        
        # 加载KWS模型（必需）
        if 'kws' in model_configs:
            kws_config = model_configs['kws']
            self.kws_model = KWSModel(
                model_path=kws_config['model_path'],
                keywords=kws_config['keywords'],
                device=self.config.get('device', 'cpu')
            )
        
        # 加载降噪模型（可选）
        if 'denoise' in model_configs:
            denoise_config = model_configs['denoise']
            self.denoise_model = DenoiseModel(
                model_path=denoise_config['model_path'],
                device=self.config.get('device', 'cpu')
            )
        
        # 加载ASR模型（可选）
        if 'asr' in model_configs:
            asr_config = model_configs['asr']
            self.asr_model = ASRModel(
                model_path=asr_config['model_path'],
                device=self.config.get('device', 'cpu')
            )
        
        # 加载TTS模型（可选）
        if 'tts' in model_configs:
            tts_config = model_configs['tts']
            self.tts_model = TTSModel(
                model_path=tts_config['model_path'],
                device=self.config.get('device', 'cpu')
            )
    
    def _setup_callbacks(self):
        """设置回调函数"""
        if self.voice_service:
            self.voice_service.set_wake_word_callback(self._on_wake_word_detected)
            self.voice_service.set_speech_recognition_callback(self._on_speech_recognized)
    
    async def process_audio_chunk(self, audio_data: bytes) -> Dict:
        """
        处理音频数据块
        
        Args:
            audio_data: 音频数据
            
        Returns:
            处理结果
        """
        if not self.is_initialized:
            raise RuntimeError("系统未初始化")
        
        if not self.voice_service:
            raise RuntimeError("语音服务未初始化")
        
        # 使用语音服务处理音频
        result = self.voice_service.process_audio(audio_data)
        
        return result
    
    async def generate_response(self, text: str) -> Optional[str]:
        """
        生成语音响应
        
        Args:
            text: 响应文本
            
        Returns:
            音频文件路径
        """
        if not self.tts_model:
            self.logger.warning("TTS模型未加载，无法生成语音响应")
            return None
        
        audio_path = self.tts_model.synthesize_for_response(text)
        
        if audio_path and self.on_response_generated:
            await self.on_response_generated(text, audio_path)
        
        return audio_path
    
    def _on_wake_word_detected(self, detection_info: Dict):
        """唤醒词检测回调"""
        self.logger.info(f"检测到唤醒词: {detection_info}")
        
        if self.on_wake_word_detected:
            asyncio.create_task(self.on_wake_word_detected(detection_info))
    
    def _on_speech_recognized(self, text: str):
        """语音识别回调"""
        self.logger.info(f"识别到语音: {text}")
        
        if self.on_speech_recognized:
            asyncio.create_task(self.on_speech_recognized(text))
    
    def start_listening(self):
        """开始监听"""
        if self.voice_service:
            self.voice_service.start_listening()
            self.is_listening = True
    
    def stop_listening(self):
        """停止监听"""
        if self.voice_service:
            self.voice_service.stop_listening()
            self.is_listening = False
    
    def get_status(self) -> Dict:
        """获取系统状态"""
        return {
            'is_initialized': self.is_initialized,
            'is_listening': self.is_listening,
            'models_loaded': {
                'kws': self.kws_model is not None,
                'asr': self.asr_model is not None,
                'denoise': self.denoise_model is not None,
                'tts': self.tts_model is not None
            }
        }
