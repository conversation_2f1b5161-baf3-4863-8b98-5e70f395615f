"""
Suziren 语音交互系统

一个基于深度学习的语音交互系统，专为湖南高速公路展厅设计。

核心功能：
- 唤醒词检测 (KWS)
- 自动语音识别 (ASR) 
- 语音降噪 (Denoise)
- 文本转语音 (TTS)
- 实时音频处理
- WebSocket音频流
- Gradio控制界面
"""

__version__ = "2.0.0"
__author__ = "Suziren Team"

# 导入核心组件
from models import KWSModel, ASRModel, DenoiseModel, TTSModel
from services import AudioProcessor, VoiceInteractionService, WebSocketAudioHandler
from core import AudioInteractionSystem
from ui import GradioInterface
from utils import setup_logger
from config import MODEL_PATHS, AUDIO_CONFIG, validate_config

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    
    # 模型组件
    "KWSModel",
    "ASRModel", 
    "DenoiseModel",
    "TTSModel",
    
    # 服务组件
    "AudioProcessor",
    "VoiceInteractionService",
    "WebSocketAudioHandler",
    
    # 核心组件
    "AudioInteractionSystem",
    
    # UI组件
    "GradioInterface",
    
    # 工具组件
    "setup_logger",
    
    # 配置
    "MODEL_PATHS",
    "AUDIO_CONFIG",
    "validate_config"
]
