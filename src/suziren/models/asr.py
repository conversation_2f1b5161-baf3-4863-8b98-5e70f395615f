"""
ASR (自动语音识别) 模型封装
"""

import os
import time
from typing import Dict, Union
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks


class ASRModel:
    """自动语音识别模型"""
    
    def __init__(self, model_path: str, device: str = 'cpu'):
        """
        初始化ASR模型
        
        Args:
            model_path: 模型路径
            device: 设备类型 ('cpu' 或 'cuda')
        """
        self.model_path = model_path
        self.device = device
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
        
        print(f"正在加载ASR模型: {self.model_path}")
        start_time = time.time()
        
        self.model = pipeline(
            task=Tasks.auto_speech_recognition,
            model=self.model_path
        )
        
        load_time = time.time() - start_time
        print(f"✓ ASR模型加载完成，耗时: {load_time:.2f}秒")
    
    def transcribe(self, audio_input: Union[str, bytes]) -> Dict:
        """
        语音转文字
        
        Args:
            audio_input: 音频输入，可以是文件路径或音频数据
            
        Returns:
            识别结果字典
        """
        if self.model is None:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        result = self.model(audio_in=audio_input)
        inference_time = time.time() - start_time
        
        # 添加推理时间到结果中
        result['inference_time'] = inference_time
        
        return result
    
    def get_text(self, result: Dict) -> str:
        """
        从结果中提取文本
        
        Args:
            result: transcribe方法返回的结果
            
        Returns:
            识别的文本
        """
        if isinstance(result, dict):
            return result.get('text', '')
        elif isinstance(result, str):
            return result
        else:
            return str(result)
