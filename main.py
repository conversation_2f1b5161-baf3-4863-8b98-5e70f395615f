#!/usr/bin/env python3
"""
Suziren 语音交互系统主入口

专为湖南高速公路展厅设计的模块化语音交互系统
支持实时音频流处理、唤醒词检测、语音识别和TTS响应

架构特点:
- 模块化管道: 降噪 → 唤醒检测 → VAD → ASR → TTS
- 异步处理: FastAPI + WebSocket + 异步任务调度
- 多接口: Gradio控制台 + WebSocket API
- 配置驱动: 所有参数通过config.py加载
"""

import asyncio
import logging
import os
import sys
import threading
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from config import (
    MODEL_PATHS, AUDIO_CONFIG, WEBSOCKET_CONFIG,
    GRADIO_CONFIG, SYSTEM_CONFIG, LOG_CONFIG, validate_config
)
from src.suziren.core import AudioInteractionSystem
from src.suziren.services import WebSocketAudioHandler
from src.suziren.ui import GradioInterface
from src.suziren.utils import setup_logger


class SuzirenApplication:
    """Suziren应用程序主类"""

    def __init__(self):
        """初始化应用程序"""
        self.logger = setup_logger("suziren_main")
        self.system: AudioInteractionSystem = None
        self.websocket_handler: WebSocketAudioHandler = None
        self.gradio_interface: GradioInterface = None
        self.app: FastAPI = None

        # 验证配置
        self._validate_configuration()

    def _validate_configuration(self):
        """验证系统配置"""
        self.logger.info("验证系统配置...")
        errors = validate_config()

        if errors:
            self.logger.error("配置验证失败:")
            for error in errors:
                self.logger.error(f"  - {error}")
            raise RuntimeError("配置验证失败，请检查配置文件")

        self.logger.info("✓ 配置验证通过")

    async def initialize_system(self):
        """初始化音频交互系统"""
        self.logger.info("初始化音频交互系统...")

        # 构建系统配置
        system_config = {
            'models': MODEL_PATHS,
            'audio': AUDIO_CONFIG,
            'system': SYSTEM_CONFIG,
            'device': 'cpu'  # 可以从环境变量或配置文件读取
        }

        # 创建音频交互系统
        self.system = AudioInteractionSystem(system_config)

        # 设置系统回调
        self.system.on_wake_word_detected = self._on_wake_word_detected
        self.system.on_speech_recognized = self._on_speech_recognized
        self.system.on_response_generated = self._on_response_generated

        # 初始化系统
        await self.system.initialize()

        self.logger.info("✓ 音频交互系统初始化完成")

    def create_fastapi_app(self) -> FastAPI:
        """创建FastAPI应用"""

        @asynccontextmanager
        async def lifespan(app: FastAPI):
            """应用生命周期管理"""
            # 启动时初始化
            await self.initialize_system()
            self.websocket_handler = WebSocketAudioHandler(self.system)
            self.logger.info("✓ FastAPI应用启动完成")

            yield

            # 关闭时清理
            self.logger.info("正在关闭应用...")

        # 创建FastAPI应用
        app = FastAPI(
            title="Suziren 语音交互系统",
            description="湖南高速公路展厅专用语音交互API",
            version="1.0.0",
            lifespan=lifespan
        )

        # 添加CORS中间件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # 静态文件服务（如果需要）
        if os.path.exists("static"):
            app.mount("/static", StaticFiles(directory="static"), name="static")

        return app

    def setup_websocket_routes(self, app: FastAPI):
        """设置WebSocket路由"""

        @app.websocket("/audio")
        async def audio_stream_endpoint(websocket: WebSocket):
            """音频流WebSocket端点"""
            client_id = f"client_{int(time.time() * 1000)}"

            try:
                await self.websocket_handler.connect(websocket, client_id)
                await self.websocket_handler.handle_audio_stream(websocket, client_id)
            except WebSocketDisconnect:
                self.websocket_handler.disconnect(client_id)
            except Exception as e:
                self.logger.error(f"WebSocket错误: {e}")
                self.websocket_handler.disconnect(client_id)

        @app.websocket("/control")
        async def control_endpoint(websocket: WebSocket):
            """控制WebSocket端点"""
            client_id = f"control_{int(time.time() * 1000)}"

            try:
                await self.websocket_handler.connect(websocket, client_id)

                while True:
                    # 接收控制消息
                    message = await websocket.receive_json()
                    await self.websocket_handler.handle_control_message(
                        websocket, client_id, message
                    )

            except WebSocketDisconnect:
                self.websocket_handler.disconnect(client_id)
            except Exception as e:
                self.logger.error(f"控制WebSocket错误: {e}")
                self.websocket_handler.disconnect(client_id)

    def setup_http_routes(self, app: FastAPI):
        """设置HTTP路由"""

        @app.get("/")
        async def root():
            """根路径"""
            return {
                "message": "Suziren 语音交互系统",
                "version": "1.0.0",
                "status": "running",
                "endpoints": {
                    "websocket_audio": "/audio",
                    "websocket_control": "/control",
                    "gradio_interface": f"http://localhost:{GRADIO_CONFIG['port']}",
                    "system_status": "/status"
                }
            }

        @app.get("/status")
        async def get_status():
            """获取系统状态"""
            if self.system:
                return {
                    "system": self.system.get_status(),
                    "websocket_connections": self.websocket_handler.get_connection_count() if self.websocket_handler else 0,
                    "timestamp": time.time()
                }
            else:
                return {"error": "系统未初始化"}

        @app.get("/health")
        async def health_check():
            """健康检查"""
            return {"status": "healthy", "timestamp": time.time()}

    async def _on_wake_word_detected(self, detection_info: Dict):
        """唤醒词检测回调"""
        self.logger.info(f"🎤 检测到唤醒词: {detection_info}")

        # 广播唤醒词检测事件
        if self.websocket_handler:
            await self.websocket_handler.broadcast_message({
                "type": "wake_word_detected",
                "detection_info": detection_info,
                "timestamp": time.time()
            })

    async def _on_speech_recognized(self, text: str):
        """语音识别回调"""
        self.logger.info(f"🗣️ 识别到语音: {text}")

        # 广播语音识别结果
        if self.websocket_handler:
            await self.websocket_handler.broadcast_message({
                "type": "speech_recognized",
                "text": text,
                "timestamp": time.time()
            })

    async def _on_response_generated(self, text: str, audio_path: str):
        """响应生成回调"""
        self.logger.info(f"🔊 生成响应: {text} -> {audio_path}")

        # 广播响应生成事件
        if self.websocket_handler:
            await self.websocket_handler.broadcast_message({
                "type": "response_generated",
                "text": text,
                "audio_path": audio_path,
                "timestamp": time.time()
            })

    def start_gradio_interface(self):
        """在单独线程中启动Gradio界面"""
        def run_gradio():
            try:
                # 等待系统初始化完成
                while not self.system or not self.system.is_initialized:
                    time.sleep(1)

                self.gradio_interface = GradioInterface(self.system, GRADIO_CONFIG)
                self.gradio_interface.launch()

            except Exception as e:
                self.logger.error(f"Gradio界面启动失败: {e}")

        gradio_thread = threading.Thread(target=run_gradio, daemon=True)
        gradio_thread.start()
        self.logger.info(f"✓ Gradio界面将在 http://localhost:{GRADIO_CONFIG['port']} 启动")

    def run(self):
        """运行应用程序"""
        self.logger.info("=" * 60)
        self.logger.info("🚀 启动 Suziren 语音交互系统")
        self.logger.info("=" * 60)

        try:
            # 创建FastAPI应用
            self.app = self.create_fastapi_app()

            # 设置路由
            self.setup_websocket_routes(self.app)
            self.setup_http_routes(self.app)

            # 启动Gradio界面（在单独线程中）
            self.start_gradio_interface()

            # 启动FastAPI服务器
            self.logger.info(f"🌐 启动WebSocket服务器: {WEBSOCKET_CONFIG['host']}:{WEBSOCKET_CONFIG['port']}")

            uvicorn.run(
                self.app,
                host=WEBSOCKET_CONFIG["host"],
                port=WEBSOCKET_CONFIG["port"],
                log_level="info",
                access_log=True
            )

        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在关闭...")
        except Exception as e:
            self.logger.error(f"应用程序运行失败: {e}")
            raise
        finally:
            self.logger.info("✓ 应用程序已关闭")


def main():
    """主函数"""
    try:
        app = SuzirenApplication()
        app.run()
    except Exception as e:
        logging.error(f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
