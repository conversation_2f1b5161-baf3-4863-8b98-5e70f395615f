#!/usr/bin/env python3
"""
Suziren语音交互系统安装检查脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✓ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"✗ Python版本过低: {version.major}.{version.minor}.{version.micro} (需要 >= 3.8)")
        return False

def check_conda_env():
    """检查conda环境"""
    print("检查conda环境...")
    try:
        conda_env = os.environ.get('CONDA_DEFAULT_ENV')
        if conda_env == 'suziren':
            print(f"✓ conda环境: {conda_env}")
            return True
        else:
            print(f"✗ 当前环境: {conda_env} (应该是 suziren)")
            return False
    except Exception as e:
        print(f"✗ conda环境检查失败: {e}")
        return False

def check_gpu():
    """检查GPU"""
    print("检查GPU...")
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"✓ GPU可用: {gpu_count} 个GPU")
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"  GPU {i}: {gpu_name}")
            return True
        else:
            print("✗ GPU不可用")
            return False
    except ImportError:
        print("✗ PyTorch未安装")
        return False

def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    required_packages = [
        'torch', 'transformers', 'modelscope', 'funasr',
        'fastapi', 'uvicorn', 'gradio', 'websockets',
        'librosa', 'soundfile', 'webrtcvad', 'pydub'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (未安装)")
            missing_packages.append(package)
    
    return len(missing_packages) == 0, missing_packages

def check_model_files():
    """检查模型文件"""
    print("检查模型文件...")
    model_base = "/pic/suziren/models"
    
    models = {
        "ASR": f"{model_base}/asr/iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch/model.pt",
        "KWS": f"{model_base}/kws/iic/speech_charctc_kws_phone-xiaoyun/finetune_avg_10.pt",
        "Denoise": f"{model_base}/denoise/iic/speech_zipenhancer_ans_multiloss_16k_base/pytorch_model.bin",
        "LLM": f"{model_base}/llm/Qwen/Qwen3-8B/config.json",
    }
    
    # TTS模型路径可能有编码问题，需要动态查找
    tts_paths = list(Path(f"{model_base}/tts").rglob("*.pt"))
    if tts_paths:
        models["TTS"] = str(tts_paths[0])
    else:
        models["TTS"] = f"{model_base}/tts/iic/CosyVoice2-0.5B/llm.pt"
    
    all_exist = True
    for model_name, model_path in models.items():
        if os.path.exists(model_path):
            print(f"✓ {model_name}: {model_path}")
        else:
            print(f"✗ {model_name}: {model_path} (不存在)")
            all_exist = False
    
    return all_exist

def check_ports():
    """检查端口是否可用"""
    print("检查端口...")
    import socket
    
    ports = [7860, 8000]  # Gradio和FastAPI端口
    all_available = True
    
    for port in ports:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            result = sock.connect_ex(('localhost', port))
            if result == 0:
                print(f"✗ 端口 {port} 已被占用")
                all_available = False
            else:
                print(f"✓ 端口 {port} 可用")
        except Exception as e:
            print(f"? 端口 {port} 检查失败: {e}")
        finally:
            sock.close()
    
    return all_available

def main():
    """主函数"""
    print("=" * 60)
    print("Suziren语音交互系统 - 安装检查")
    print("=" * 60)
    
    checks = []
    
    # 执行各项检查
    checks.append(("Python版本", check_python_version()))
    checks.append(("conda环境", check_conda_env()))
    checks.append(("GPU支持", check_gpu()))
    
    deps_ok, missing = check_dependencies()
    checks.append(("依赖包", deps_ok))
    
    checks.append(("模型文件", check_model_files()))
    checks.append(("端口可用性", check_ports()))
    
    print("\n" + "=" * 60)
    print("检查结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for check_name, result in checks:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{check_name:15}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有检查通过！系统已准备就绪。")
        print("\n下一步:")
        print("1. 运行 python main.py 启动系统")
        print("2. 访问 http://localhost:7860 使用Gradio界面")
        print("3. 或使用 WebSocket 连接 ws://localhost:8000/ws")
    else:
        print("❌ 部分检查失败，请解决以下问题:")
        
        if not deps_ok:
            print(f"\n缺失的依赖包: {', '.join(missing)}")
            print("安装命令: pip install " + " ".join(missing))
        
        print("\n请解决问题后重新运行此脚本。")
    
    print("=" * 60)
    return all_passed

if __name__ == "__main__":
    main()
