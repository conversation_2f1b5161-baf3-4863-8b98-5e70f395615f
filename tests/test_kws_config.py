#!/usr/bin/env python3
"""
使用config.py配置测试唤醒词检测功能
"""

import os
import sys
import time
import requests

# Add parent directory to path to import config
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from suziren.config import MODEL_PATHS
from suziren.models import KWSModel, DenoiseModel
from suziren.services import VoiceInteractionService
from suziren.utils import setup_logger

def test_kws_with_config():
    """使用config.py配置测试唤醒词检测"""
    print("=" * 60)
    print("Suziren - 唤醒词检测测试 (使用config.py)")
    print("=" * 60)

    try:
        # 设置日志
        logger = setup_logger("test_kws")

        # 从config.py获取模型配置
        kws_config = MODEL_PATHS["kws"]
        model_path = kws_config["model_path"]
        keywords = kws_config["keywords"]

        print(f"模型路径: {model_path}")
        print(f"关键词: {keywords}")
        print("设备: CPU")

        # 检查模型路径是否存在
        if not os.path.exists(model_path):
            print(f"✗ 模型路径不存在: {model_path}")
            return False

        print(f"✓ 模型路径存在")

        print("\n正在加载模型...")

        # 使用新的KWSModel类
        kws_model = KWSModel(
            model_path=model_path,
            keywords=keywords,
            device='cpu'
        )

        # 测试音频URL
        test_wav = "https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/KWS/pos_testset/kws_xiaoyunxiaoyun.wav"
        print(f"\n测试音频: {test_wav}")

        # 开始推理
        print("开始唤醒词检测...")

        res = kws_model.detect(test_wav)

        print(f"✓ 推理完成，耗时: {res.get('inference_time', 0):.2f}秒")
        print("\n" + "=" * 40)
        print("检测结果:")
        print("=" * 40)
        print(res)
        print("=" * 40)

        # 使用新的KWSModel方法检查结果
        is_detected = kws_model.is_wake_word_detected(res)
        print(f"\n是否检测到唤醒词: {is_detected}")

        if is_detected:
            detection_info = kws_model.get_detection_info(res)
            print(f"检测信息: {detection_info}")
            print("🎉 唤醒词检测成功！")
            return True
        else:
            print("⚠️  未检测到唤醒词")
            return False
            
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        print("请确保已安装 funasr: pip install funasr")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_local_audio_with_config():
    """使用config.py配置测试本地音频"""
    print("\n" + "=" * 60)
    print("测试本地音频文件 (使用config.py)")
    print("=" * 60)
    
    # 下载测试音频
    test_wav_url = "https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/KWS/pos_testset/kws_xiaoyunxiaoyun.wav"
    local_wav_path = "test_xiaoyun.wav"
    
    try:
        print(f"下载测试音频: {local_wav_path}")
        response = requests.get(test_wav_url, timeout=30)
        response.raise_for_status()
        
        with open(local_wav_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✓ 音频下载完成，大小: {len(response.content):,} 字节")
        
        # 使用config.py配置
        kws_config = MODEL_PATHS["kws"]
        model_path = kws_config["model_path"]
        keywords = kws_config["keywords"]

        print(f"\n使用模型路径: {model_path}")
        print(f"关键词: {keywords}")

        print("正在加载模型...")
        kws_model = KWSModel(
            model_path=model_path,
            keywords=keywords,
            device='cpu'
        )

        print("开始本地音频检测...")

        res = kws_model.detect(local_wav_path)

        print(f"✓ 本地音频推理完成，耗时: {res.get('inference_time', 0):.2f}秒")
        
        print("\n本地音频检测结果:")
        print("=" * 40)
        print(res)
        print("=" * 40)
        
        # 清理临时文件
        if os.path.exists(local_wav_path):
            os.remove(local_wav_path)
            print(f"✓ 清理临时文件: {local_wav_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 本地音频测试失败: {e}")
        # 清理临时文件
        if os.path.exists(local_wav_path):
            os.remove(local_wav_path)
        return False

def test_model_files():
    """检查模型文件完整性"""
    print("\n" + "=" * 60)
    print("检查模型文件完整性")
    print("=" * 60)
    
    kws_config = MODEL_PATHS["kws"]
    model_path = kws_config["model_path"]
    
    print(f"模型路径: {model_path}")
    
    if not os.path.exists(model_path):
        print(f"✗ 模型路径不存在: {model_path}")
        return False
    
    # 检查关键文件
    key_files = [
        "finetune_avg_10.pt",
        "config.yaml",
        "configuration.json"
    ]
    
    all_exist = True
    for file_name in key_files:
        file_path = os.path.join(model_path, file_name)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✓ {file_name}: {file_size:,} 字节")
        else:
            print(f"✗ {file_name}: 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("Suziren语音交互系统 - 唤醒词检测测试 (使用config.py)")
    
    # 检查模型文件
    files_ok = test_model_files()
    
    if not files_ok:
        print("\n❌ 模型文件检查失败")
        return False
    
    # 测试在线音频
    online_success = test_kws_with_config()
    
    # 测试本地音频
    local_success = test_local_audio_with_config()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"模型文件检查: {'✓ 通过' if files_ok else '✗ 失败'}")
    print(f"在线音频测试: {'✓ 成功' if online_success else '✗ 失败'}")
    print(f"本地音频测试: {'✓ 成功' if local_success else '✗ 失败'}")
    
    if online_success or local_success:
        print("\n🎉 唤醒词检测功能正常！")
        print("✅ 可以继续开发其他模块")
        print("\n下一步建议:")
        print("1. 测试ASR模型")
        print("2. 测试降噪模型") 
        print("3. 开发音频处理模块")
        print("4. 开发WebSocket服务")
        return True
    else:
        print("\n❌ 唤醒词检测测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
