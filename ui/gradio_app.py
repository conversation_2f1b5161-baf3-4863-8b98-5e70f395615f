"""
Gradio 控制界面
"""

import gradio as gr
import asyncio
import logging
from typing import Dict, Any, Optional
from core import AudioInteractionSystem


class GradioInterface:
    """Gradio控制界面"""
    
    def __init__(self, system: AudioInteractionSystem, config: Dict[str, Any]):
        """
        初始化Gradio界面
        
        Args:
            system: 音频交互系统实例
            config: Gradio配置
        """
        self.system = system
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.demo: Optional[gr.Blocks] = None
        
        # 界面状态
        self.status_text = "系统待机"
        self.log_messages = []
    
    def create_interface(self) -> gr.Blocks:
        """创建Gradio界面"""
        with gr.Blocks(
            title="Suziren 语音交互控制台",
            theme=gr.themes.Soft(),
            css="""
            .status-box { 
                background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
            .control-button {
                margin: 5px;
                padding: 10px 20px;
                border-radius: 8px;
            }
            """
        ) as demo:
            
            gr.Markdown("# 🎤 Suziren 语音交互系统")
            gr.Markdown("湖南高速公路展厅专用语音交互控制台")
            
            with gr.Row():
                with gr.Column(scale=2):
                    # 系统状态显示
                    with gr.Group(elem_classes="status-box"):
                        gr.Markdown("## 📊 系统状态")
                        status_display = gr.Textbox(
                            value=self.status_text,
                            label="当前状态",
                            interactive=False,
                            lines=2
                        )
                        
                        system_info = gr.JSON(
                            value=self.system.get_status(),
                            label="系统信息"
                        )
                    
                    # 控制按钮
                    with gr.Group():
                        gr.Markdown("## 🎛️ 系统控制")
                        with gr.Row():
                            start_btn = gr.Button(
                                "🎤 开始监听",
                                variant="primary",
                                elem_classes="control-button"
                            )
                            stop_btn = gr.Button(
                                "🔇 停止监听",
                                variant="secondary",
                                elem_classes="control-button"
                            )
                            refresh_btn = gr.Button(
                                "🔄 刷新状态",
                                variant="secondary",
                                elem_classes="control-button"
                            )
                
                with gr.Column(scale=3):
                    # 音频输入测试
                    with gr.Group():
                        gr.Markdown("## 🎵 音频测试")
                        
                        # 麦克风输入
                        audio_input = gr.Audio(
                            sources=["microphone"],
                            type="filepath",
                            label="麦克风输入"
                        )
                        
                        # 文件上传
                        file_input = gr.File(
                            file_types=[".wav", ".mp3", ".m4a"],
                            label="上传音频文件"
                        )
                        
                        test_btn = gr.Button(
                            "🧪 测试音频",
                            variant="primary"
                        )
                    
                    # 结果显示
                    with gr.Group():
                        gr.Markdown("## 📝 处理结果")
                        result_display = gr.JSON(
                            label="最新结果",
                            value={}
                        )
            
            # TTS测试区域
            with gr.Row():
                with gr.Group():
                    gr.Markdown("## 🔊 TTS测试")
                    tts_input = gr.Textbox(
                        placeholder="输入要合成的文本...",
                        label="文本输入",
                        lines=2
                    )
                    tts_btn = gr.Button("🎵 生成语音", variant="primary")
                    tts_output = gr.Audio(label="生成的语音")
            
            # 日志显示
            with gr.Row():
                with gr.Group():
                    gr.Markdown("## 📋 系统日志")
                    log_display = gr.Textbox(
                        value="",
                        label="实时日志",
                        lines=8,
                        max_lines=20,
                        interactive=False,
                        autoscroll=True
                    )
            
            # 绑定事件处理函数
            start_btn.click(
                fn=self._start_listening,
                outputs=[status_display, system_info, log_display]
            )
            
            stop_btn.click(
                fn=self._stop_listening,
                outputs=[status_display, system_info, log_display]
            )
            
            refresh_btn.click(
                fn=self._refresh_status,
                outputs=[status_display, system_info]
            )
            
            test_btn.click(
                fn=self._test_audio,
                inputs=[audio_input, file_input],
                outputs=[result_display, log_display]
            )
            
            tts_btn.click(
                fn=self._test_tts,
                inputs=[tts_input],
                outputs=[tts_output, log_display]
            )
        
        self.demo = demo
        return demo
    
    def _start_listening(self):
        """开始监听"""
        try:
            self.system.start_listening()
            self.status_text = "🎤 正在监听唤醒词..."
            self._add_log("开始监听唤醒词")
            return self.status_text, self.system.get_status(), self._get_log_text()
        except Exception as e:
            error_msg = f"启动监听失败: {e}"
            self._add_log(error_msg)
            return f"❌ {error_msg}", self.system.get_status(), self._get_log_text()
    
    def _stop_listening(self):
        """停止监听"""
        try:
            self.system.stop_listening()
            self.status_text = "🔇 已停止监听"
            self._add_log("停止监听")
            return self.status_text, self.system.get_status(), self._get_log_text()
        except Exception as e:
            error_msg = f"停止监听失败: {e}"
            self._add_log(error_msg)
            return f"❌ {error_msg}", self.system.get_status(), self._get_log_text()
    
    def _refresh_status(self):
        """刷新状态"""
        status = self.system.get_status()
        if status['is_listening']:
            self.status_text = "🎤 正在监听唤醒词..."
        else:
            self.status_text = "🔇 系统待机"
        
        self._add_log("状态已刷新")
        return self.status_text, status
    
    def _test_audio(self, mic_audio, file_audio):
        """测试音频处理"""
        try:
            # 选择音频源
            audio_path = mic_audio if mic_audio else file_audio
            if not audio_path:
                return {}, self._get_log_text()
            
            self._add_log(f"开始处理音频: {audio_path}")
            
            # 这里需要同步调用异步方法
            # 在实际应用中，可能需要使用线程池或其他方式处理
            result = {"message": "音频处理功能需要在异步环境中运行"}
            
            self._add_log("音频处理完成")
            return result, self._get_log_text()
            
        except Exception as e:
            error_msg = f"音频处理失败: {e}"
            self._add_log(error_msg)
            return {"error": error_msg}, self._get_log_text()
    
    def _test_tts(self, text):
        """测试TTS合成"""
        try:
            if not text.strip():
                return None, self._get_log_text()
            
            self._add_log(f"开始TTS合成: {text}")
            
            # 这里需要同步调用异步方法
            # 在实际应用中，可能需要使用线程池或其他方式处理
            self._add_log("TTS合成功能需要在异步环境中运行")
            
            return None, self._get_log_text()
            
        except Exception as e:
            error_msg = f"TTS合成失败: {e}"
            self._add_log(error_msg)
            return None, self._get_log_text()
    
    def _add_log(self, message: str):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_messages.append(log_entry)
        
        # 保持最近50条日志
        if len(self.log_messages) > 50:
            self.log_messages = self.log_messages[-50:]
    
    def _get_log_text(self) -> str:
        """获取日志文本"""
        return "\n".join(self.log_messages)
    
    def launch(self, **kwargs):
        """启动Gradio界面"""
        if not self.demo:
            self.create_interface()
        
        # 合并配置
        launch_config = {
            "server_name": self.config.get("host", "0.0.0.0"),
            "server_port": self.config.get("port", 7860),
            "share": self.config.get("share", False),
            "debug": self.config.get("debug", True),
            **kwargs
        }
        
        self.logger.info(f"启动Gradio界面: {launch_config}")
        return self.demo.launch(**launch_config)
