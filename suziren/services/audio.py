"""
音频处理服务
"""

import numpy as np
import soundfile as sf
from typing import Union, Tuple


class AudioProcessor:
    """音频处理器"""
    
    def __init__(self, sample_rate: int = 16000):
        """
        初始化音频处理器
        
        Args:
            sample_rate: 采样率
        """
        self.sample_rate = sample_rate
    
    def load_audio(self, audio_path: str) -> Tuple[np.ndarray, int]:
        """
        加载音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            (音频数据, 采样率)
        """
        audio_data, sr = sf.read(audio_path)
        
        # 如果采样率不匹配，进行重采样
        if sr != self.sample_rate:
            audio_data = self.resample(audio_data, sr, self.sample_rate)
            sr = self.sample_rate
        
        return audio_data, sr
    
    def save_audio(self, audio_data: np.ndarray, output_path: str, 
                   sample_rate: int = None):
        """
        保存音频文件
        
        Args:
            audio_data: 音频数据
            output_path: 输出路径
            sample_rate: 采样率
        """
        if sample_rate is None:
            sample_rate = self.sample_rate
        
        sf.write(output_path, audio_data, sample_rate)
    
    def resample(self, audio_data: np.ndarray, orig_sr: int, 
                 target_sr: int) -> np.ndarray:
        """
        音频重采样
        
        Args:
            audio_data: 原始音频数据
            orig_sr: 原始采样率
            target_sr: 目标采样率
            
        Returns:
            重采样后的音频数据
        """
        try:
            import librosa
            return librosa.resample(audio_data, orig_sr=orig_sr, target_sr=target_sr)
        except ImportError:
            # 简单的线性插值重采样（质量较低但无需额外依赖）
            ratio = target_sr / orig_sr
            new_length = int(len(audio_data) * ratio)
            indices = np.linspace(0, len(audio_data) - 1, new_length)
            return np.interp(indices, np.arange(len(audio_data)), audio_data)
    
    def normalize_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """
        音频归一化
        
        Args:
            audio_data: 音频数据
            
        Returns:
            归一化后的音频数据
        """
        max_val = np.max(np.abs(audio_data))
        if max_val > 0:
            return audio_data / max_val
        return audio_data
    
    def trim_silence(self, audio_data: np.ndarray, 
                     threshold: float = 0.01) -> np.ndarray:
        """
        去除静音部分
        
        Args:
            audio_data: 音频数据
            threshold: 静音阈值
            
        Returns:
            去除静音后的音频数据
        """
        # 找到非静音部分
        non_silent = np.abs(audio_data) > threshold
        if not np.any(non_silent):
            return audio_data
        
        # 找到第一个和最后一个非静音样本
        start_idx = np.argmax(non_silent)
        end_idx = len(audio_data) - np.argmax(non_silent[::-1]) - 1
        
        return audio_data[start_idx:end_idx + 1]
