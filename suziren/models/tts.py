"""
TTS (Text-to-Speech) 模型封装
"""

import os
import time
import tempfile
from typing import Union, Dict, Optional
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks


class TTSModel:
    """文本转语音模型"""
    
    def __init__(self, model_path: str, device: str = 'cpu'):
        """
        初始化TTS模型
        
        Args:
            model_path: 模型路径
            device: 设备类型 ('cpu' 或 'cuda')
        """
        self.model_path = model_path
        self.device = device
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
        
        print(f"正在加载TTS模型: {self.model_path}")
        start_time = time.time()
        
        self.model = pipeline(
            task=Tasks.text_to_speech,
            model=self.model_path
        )
        
        load_time = time.time() - start_time
        print(f"✓ TTS模型加载完成，耗时: {load_time:.2f}秒")
    
    def synthesize(self, text: str, output_path: Optional[str] = None) -> Dict:
        """
        文本转语音
        
        Args:
            text: 输入文本
            output_path: 输出音频文件路径，如果为None则使用临时文件
            
        Returns:
            合成结果字典
        """
        if self.model is None:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        
        # 如果没有指定输出路径，使用临时文件
        if output_path is None:
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            output_path = temp_file.name
            temp_file.close()
        
        try:
            # 执行TTS合成
            result = self.model(input=text, output_path=output_path)
            
            inference_time = time.time() - start_time
            
            # 返回结果字典
            return {
                'output_path': output_path,
                'inference_time': inference_time,
                'success': True,
                'text': text,
                'audio_info': result.get('output_info', {})
            }
            
        except Exception as e:
            inference_time = time.time() - start_time
            return {
                'output_path': None,
                'inference_time': inference_time,
                'success': False,
                'error': str(e),
                'text': text
            }
    
    def synthesize_for_response(self, text: str) -> Optional[str]:
        """
        为响应生成语音，返回音频文件路径
        
        Args:
            text: 响应文本
            
        Returns:
            音频文件路径，失败时返回None
        """
        result = self.synthesize(text)
        
        if result['success']:
            print(f"✓ TTS合成完成，耗时: {result['inference_time']:.2f}秒")
            return result['output_path']
        else:
            print(f"✗ TTS合成失败: {result['error']}")
            return None
    
    def get_model_info(self) -> Dict:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            'model_path': self.model_path,
            'device': self.device,
            'is_loaded': self.model is not None
        }
