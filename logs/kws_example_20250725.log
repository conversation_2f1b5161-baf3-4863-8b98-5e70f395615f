2025-07-25 17:51:41,370 - kws_example - INFO - 初始化KWS模型: /pic/suziren/models/kws/iic/speech_charctc_kws_phone-xiaoyun
2025-07-25 17:51:41,370 - kws_example - INFO - 关键词: ['小云小云']
2025-07-25 17:51:42,277 - kws_example - INFO - 检测音频: https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/KWS/pos_testset/kws_xiaoyunxiaoyun.wav
2025-07-25 17:51:42,598 - kws_example - INFO - 检测结果:
2025-07-25 17:51:42,598 - kws_example - INFO -   原始结果: {'kws_type': 'pcm', 'kws_list': [{'keyword': '小云小云', 'offset': 1.89, 'length': 0.51, 'confidence': 0.995018, 'type': 'wakeup'}], 'wav_count': 1, 'inference_time': 0.3208787441253662}
2025-07-25 17:51:42,598 - kws_example - INFO -   是否检测到唤醒词: True
2025-07-25 17:51:42,598 - kws_example - INFO -   检测信息: {'keyword': '小云小云', 'confidence': 0.995018, 'offset': 1.89, 'length': 0.51, 'type': 'wakeup'}
