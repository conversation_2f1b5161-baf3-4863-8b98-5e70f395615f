# Suziren 语音交互系统

湖南高速公路展厅专用语音交互系统，基于深度学习的实时语音处理。

## 🎯 功能特点

- 🎤 **唤醒词检测 (KWS)**: 支持"小云小云"唤醒
- 🗣️ **自动语音识别 (ASR)**: 实时语音转文字
- 🔊 **语音降噪**: 提升音频质量
- 🔊 **语音合成 (TTS)**: 文本转语音响应
- 🌐 **Web界面**: Gradio可视化控制台
- ⚡ **实时处理**: WebSocket音频流传输

## 🏗️ 技术架构

- **深度学习框架**: ModelScope + FunASR
- **Web框架**: FastAPI + WebSocket
- **用户界面**: Gradio
- **音频处理**: 16kHz采样率，实时流处理

## 📁 项目结构

```
suziren/                     # 项目根目录
├── models/                  # 模型层
│   ├── kws.py              # 唤醒词检测
│   ├── asr.py              # 语音识别
│   ├── denoise.py          # 降噪
│   └── tts.py              # 语音合成
├── core/                   # 核心层
│   └── system.py           # 音频交互系统
├── services/               # 服务层
│   ├── audio.py            # 音频处理
│   ├── interaction.py      # 语音交互
│   └── websocket.py        # WebSocket服务
├── api/                    # API层
│   ├── app.py              # FastAPI应用
│   └── routes/             # 路由
├── ui/                     # UI层
│   └── gradio_app.py       # Gradio界面
├── config/                 # 配置层
│   ├── settings.py         # 基础配置
│   └── models.py           # 模型配置
├── utils/                  # 工具层
├── examples/               # 示例代码
├── tests/                  # 测试代码
├── scripts/                # 工具脚本
└── main.py                 # 主入口
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 激活conda环境
conda activate suziren

# 检查Python版本 (需要3.8+)
python --version
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置模型路径

模型文件位于服务器 `/pic/suziren/models/` 目录下。如果是本地开发，可以设置环境变量：

```bash
export SUZIREN_MODEL_PATH="/your/local/model/path"
```

### 4. 启动系统

```bash
python main.py
```

### 5. 访问界面

- **Gradio控制台**: http://localhost:7860
- **WebSocket音频**: ws://localhost:8000/audio
- **系统状态**: http://localhost:8000/status

## 🔧 配置说明

### 主要配置文件

- `config/settings.py`: 基础配置（音频、WebSocket、Gradio等）
- `config/models.py`: 模型路径配置

### 关键配置项

```python
# 音频配置
AUDIO_CONFIG = {
    "sample_rate": 16000,
    "channels": 1,
    "chunk_size": 1024
}

# 模型路径
MODEL_PATHS = {
    "kws": "/pic/suziren/models/kws/...",
    "asr": "/pic/suziren/models/asr/...",
    "denoise": "/pic/suziren/models/denoise/...",
    "tts": "/pic/suziren/models/tts/..."
}
```

## 💡 使用示例

### KWS唤醒词检测

```python
from models import KWSModel
from config import MODEL_PATHS

# 初始化KWS模型
kws_model = KWSModel(
    model_path=MODEL_PATHS["kws"]["model_path"],
    keywords=MODEL_PATHS["kws"]["keywords"]
)

# 检测唤醒词
result = kws_model.detect(audio_data)
```

### 完整语音交互

```python
from core import AudioInteractionSystem
from config import MODEL_PATHS, AUDIO_CONFIG

# 创建系统配置
config = {
    'models': MODEL_PATHS,
    'audio': AUDIO_CONFIG,
    'device': 'cpu'
}

# 初始化系统
system = AudioInteractionSystem(config)
await system.initialize()

# 处理音频
result = await system.process_audio_chunk(audio_data)
```

## 🧪 测试

```bash
# 验证项目结构
python scripts/validate_main.py

# 运行KWS示例
python examples/kws_example.py

# 运行集成测试
python tests/test_main_integration.py
```

## 🔍 开发指南

### 添加新模型

1. 在 `models/` 中创建新模型类
2. 在 `config/models.py` 中添加配置
3. 在 `core/system.py` 中集成

### 添加新API

1. 在 `api/routes/` 中创建新路由
2. 在 `api/app.py` 中注册路由

### 环境变量

- `SUZIREN_MODEL_PATH`: 自定义模型路径
- `CUDA_VISIBLE_DEVICES`: GPU设备配置

## 📋 依赖说明

主要依赖包：
- `fastapi`: Web框架
- `uvicorn`: ASGI服务器
- `gradio`: Web界面
- `websockets`: WebSocket支持
- `modelscope`: 模型加载
- `funasr`: 语音识别

## 🎯 部署说明

### 服务器部署

1. 确保模型文件在 `/pic/suziren/models/`
2. 激活conda环境
3. 启动服务：`python main.py`

### 端口配置

- WebSocket服务: 8000
- Gradio界面: 7860

## 📞 联系方式

如有问题请联系开发团队。

---

**版本**: v2.0.0  
**更新时间**: 2025-07-29  
**适用环境**: 湖南高速公路展厅
