"""
HTTP路由
"""

import time
from fastapi import FastAPI
from core import AudioInteractionSystem
from services import WebSocketAudioHandler
from config import GRADIO_CONFIG


def setup_http_routes(app: FastAPI, system: AudioInteractionSystem, websocket_handler: WebSocketAudioHandler):
    """设置HTTP路由"""
    
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "Suziren 语音交互系统",
            "version": "2.0.0",
            "status": "running",
            "endpoints": {
                "websocket_audio": "/audio",
                "websocket_control": "/control",
                "gradio_interface": f"http://localhost:{GRADIO_CONFIG['port']}",
                "system_status": "/status"
            }
        }
    
    @app.get("/status")
    async def get_status():
        """获取系统状态"""
        if system:
            return {
                "system": system.get_status(),
                "websocket_connections": websocket_handler.get_connection_count() if websocket_handler else 0,
                "timestamp": time.time()
            }
        else:
            return {"error": "系统未初始化"}
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {"status": "healthy", "timestamp": time.time()}
    
    @app.get("/models")
    async def list_models():
        """列出模型状态"""
        if system:
            status = system.get_status()
            return {
                "models": status.get("models_loaded", {}),
                "timestamp": time.time()
            }
        else:
            return {"error": "系统未初始化"}
