"""
KWS (关键词检测) 模型封装
"""

import os
import time
from typing import Dict, List, Optional, Union
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks


class KWSModel:
    """唤醒词检测模型"""
    
    def __init__(self, model_path: str, keywords: List[str], device: str = 'cpu'):
        """
        初始化KWS模型
        
        Args:
            model_path: 模型路径
            keywords: 关键词列表
            device: 设备类型 ('cpu' 或 'cuda')
        """
        self.model_path = model_path
        self.keywords = keywords
        self.device = device
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
        
        print(f"正在加载KWS模型: {self.model_path}")
        start_time = time.time()
        
        self.model = pipeline(
            task=Tasks.keyword_spotting,
            model=self.model_path
        )
        
        load_time = time.time() - start_time
        print(f"✓ KWS模型加载完成，耗时: {load_time:.2f}秒")
    
    def detect(self, audio_input: Union[str, bytes]) -> Dict:
        """
        检测唤醒词
        
        Args:
            audio_input: 音频输入，可以是文件路径或音频数据
            
        Returns:
            检测结果字典
        """
        if self.model is None:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        result = self.model(audio_in=audio_input)
        inference_time = time.time() - start_time
        
        # 添加推理时间到结果中
        result['inference_time'] = inference_time
        
        return result
    
    def is_wake_word_detected(self, result: Dict, confidence_threshold: float = 0.8) -> bool:
        """
        判断是否检测到唤醒词
        
        Args:
            result: detect方法返回的结果
            confidence_threshold: 置信度阈值
            
        Returns:
            是否检测到唤醒词
        """
        if 'kws_list' not in result:
            return False
        
        for detection in result['kws_list']:
            if (detection.get('type') == 'wakeup' and 
                detection.get('confidence', 0) >= confidence_threshold):
                return True
        
        return False
    
    def get_detection_info(self, result: Dict) -> Optional[Dict]:
        """
        获取检测信息
        
        Args:
            result: detect方法返回的结果
            
        Returns:
            检测信息字典或None
        """
        if 'kws_list' not in result or not result['kws_list']:
            return None
        
        # 返回置信度最高的检测结果
        best_detection = max(result['kws_list'], 
                           key=lambda x: x.get('confidence', 0))
        
        return {
            'keyword': best_detection.get('keyword'),
            'confidence': best_detection.get('confidence'),
            'offset': best_detection.get('offset'),
            'length': best_detection.get('length'),
            'type': best_detection.get('type')
        }
