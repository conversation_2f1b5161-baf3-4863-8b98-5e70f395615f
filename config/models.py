#!/usr/bin/env python3
"""
模型配置文件
"""

import os
from config.settings import MODEL_BASE_PATH

# 模型路径配置
MODEL_PATHS = {
    # ASR模型 (Paraformer)
    "asr": {
        "model_path": f"{MODEL_BASE_PATH}/asr/iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "model_name": "iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
    },
    
    # 唤醒词检测模型 (小云小云)
    "kws": {
        "model_path": f"{MODEL_BASE_PATH}/kws/iic/speech_charctc_kws_phone-xiaoyun",
        "model_name": "iic/speech_charctc_kws_phone-xiaoyun",
        "keywords": ["小云小云"]
    },
    
    # 降噪模型 (ZipEnhancer)
    "denoise": {
        "model_path": f"{MODEL_BASE_PATH}/denoise/iic/speech_zipenhancer_ans_multiloss_16k_base",
        "model_name": "iic/speech_zipenhancer_ans_multiloss_16k_base"
    },
    
    # LLM模型 (Qwen3-8B)
    "llm": {
        "model_path": f"{MODEL_BASE_PATH}/llm/Qwen/Qwen3-8B",
        "model_name": "Qwen/Qwen3-8B"
    },
    
    # TTS模型 (CosyVoice2-0.5B)
    "tts": {
        "model_path": f"{MODEL_BASE_PATH}/tts/iic/CosyVoice2-0.5B",
        "model_name": "iic/CosyVoice2-0.5B"
    }
}

def validate_model_paths():
    """验证模型路径是否存在"""
    errors = []
    
    for model_name, config in MODEL_PATHS.items():
        model_path = config["model_path"]
        if not os.path.exists(model_path):
            errors.append(f"模型路径不存在: {model_name} -> {model_path}")
    
    return errors

def get_model_config(model_name: str):
    """获取指定模型的配置"""
    return MODEL_PATHS.get(model_name)

def list_available_models():
    """列出所有可用的模型"""
    available = []
    for model_name, config in MODEL_PATHS.items():
        if os.path.exists(config["model_path"]):
            available.append(model_name)
    return available
