# Suziren 湖南高速公路展厅语音交互系统

专为湖南高速公路展厅设计的实时语音交互系统，提供完整的语音处理流水线。

## 快速开始

### 1. 环境检查
```bash
python scripts/install_check.py
```

### 2. 模型下载
```bash
# 下载所有模型
python scripts/download_models.py

# 下载特定模型
python scripts/download_models.py llm
python scripts/download_models.py tts
```

### 3. 监控下载进度
```bash
python scripts/monitor_download.py
```

### 4. 测试唤醒词功能
```bash
python tests/test_kws_config.py
```

## 项目结构

```
suziren/
├── README.md              # 项目说明
├── config.py              # 系统配置
├── requirements.txt       # 依赖包列表
├── scripts/               # 工具脚本
│   ├── download_models.py # 模型下载工具
│   ├── install_check.py   # 安装检查
│   └── monitor_download.py # 下载监控
├── tests/                 # 测试文件
│   └── test_kws_config.py # 唤醒词测试
├── docs/                  # 文档
│   └── README.md          # 详细文档
└── src/                   # 源代码 (待创建)
    ├── audio/             # 音频处理模块
    ├── models/            # 模型加载模块
    ├── websocket/         # WebSocket服务
    ├── api/               # REST API
    └── ui/                # Gradio界面
```

## 系统特性

- 🎤 **实时语音交互**: 支持持续监听和点击触发两种模式
- 🔊 **唤醒词检测**: 使用"小云小云"作为唤醒词
- 🧠 **智能对话**: 基于Qwen3-8B的专业高速公路知识问答
- 🌐 **多接口支持**: 提供Gradio Web界面和FastAPI RESTful接口
- 📡 **WebSocket传输**: 实时音频流传输，低延迟响应

## 核心模型

- **ASR**: Paraformer-large (语音识别)
- **唤醒词检测**: CharCTC KWS (小云小云)
- **降噪**: ZipEnhancer (音频降噪)
- **LLM**: Qwen3-8B (大语言模型)
- **TTS**: CosyVoice2-0.5B (语音合成)

## 开发状态

- [x] 环境配置和依赖安装
- [x] 模型下载和验证
- [x] 唤醒词检测测试
- [ ] 音频处理模块
- [ ] WebSocket服务
- [ ] Gradio界面
- [ ] REST API接口

## 许可证

本项目为内部使用项目，请遵守相关模型的许可证要求。
