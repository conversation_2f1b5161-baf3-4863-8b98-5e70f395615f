"""
语音交互服务
"""

import time
from typing import Dict, Optional, Callable
from ..models import KWSModel, ASRModel, DenoiseModel
from .audio import AudioProcessor


class VoiceInteractionService:
    """语音交互服务"""
    
    def __init__(self, 
                 kws_model: KWSModel,
                 asr_model: ASRModel = None,
                 denoise_model: DenoiseModel = None,
                 audio_processor: AudioProcessor = None):
        """
        初始化语音交互服务
        
        Args:
            kws_model: 唤醒词检测模型
            asr_model: 语音识别模型
            denoise_model: 降噪模型
            audio_processor: 音频处理器
        """
        self.kws_model = kws_model
        self.asr_model = asr_model
        self.denoise_model = denoise_model
        self.audio_processor = audio_processor or AudioProcessor()
        
        # 回调函数
        self.on_wake_word_detected: Optional[Callable] = None
        self.on_speech_recognized: Optional[Callable] = None
        
        # 状态
        self.is_listening = False
        self.wake_word_confidence_threshold = 0.8
    
    def set_wake_word_callback(self, callback: Callable):
        """设置唤醒词检测回调"""
        self.on_wake_word_detected = callback
    
    def set_speech_recognition_callback(self, callback: Callable):
        """设置语音识别回调"""
        self.on_speech_recognized = callback
    
    def process_audio(self, audio_input) -> Dict:
        """
        处理音频输入
        
        Args:
            audio_input: 音频输入
            
        Returns:
            处理结果
        """
        result = {
            'timestamp': time.time(),
            'wake_word_detected': False,
            'speech_text': None,
            'processing_time': 0
        }
        
        start_time = time.time()
        
        try:
            # 1. 降噪处理（如果有降噪模型）
            processed_audio = audio_input
            if self.denoise_model:
                processed_audio = self.denoise_model.denoise_for_kws(audio_input)
                result['denoise_applied'] = True
            else:
                result['denoise_applied'] = False
            
            # 2. 唤醒词检测
            kws_result = self.kws_model.detect(processed_audio)
            wake_word_detected = self.kws_model.is_wake_word_detected(
                kws_result, self.wake_word_confidence_threshold
            )
            
            result['wake_word_detected'] = wake_word_detected
            result['kws_result'] = kws_result
            
            # 触发唤醒词回调
            if wake_word_detected and self.on_wake_word_detected:
                detection_info = self.kws_model.get_detection_info(kws_result)
                self.on_wake_word_detected(detection_info)
            
            # 3. 如果检测到唤醒词且有ASR模型，进行语音识别
            if wake_word_detected and self.asr_model:
                asr_result = self.asr_model.transcribe(processed_audio)
                speech_text = self.asr_model.get_text(asr_result)
                
                result['speech_text'] = speech_text
                result['asr_result'] = asr_result
                
                # 触发语音识别回调
                if self.on_speech_recognized:
                    self.on_speech_recognized(speech_text)
        
        except Exception as e:
            result['error'] = str(e)
        
        result['processing_time'] = time.time() - start_time
        return result
    
    def start_listening(self):
        """开始监听"""
        self.is_listening = True
        print("🎤 开始监听唤醒词...")
    
    def stop_listening(self):
        """停止监听"""
        self.is_listening = False
        print("🔇 停止监听")
    
    def get_status(self) -> Dict:
        """获取服务状态"""
        return {
            'is_listening': self.is_listening,
            'has_kws_model': self.kws_model is not None,
            'has_asr_model': self.asr_model is not None,
            'has_denoise_model': self.denoise_model is not None,
            'wake_word_threshold': self.wake_word_confidence_threshold
        }
