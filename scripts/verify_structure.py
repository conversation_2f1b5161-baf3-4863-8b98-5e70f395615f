#!/usr/bin/env python3
"""
项目结构验证脚本

验证重构后的项目结构是否正确，不依赖外部库
"""

import os
import sys
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"  ✅ {description}: {file_path}")
        return True
    else:
        print(f"  ❌ {description}: {file_path} (不存在)")
        return False

def check_directory_structure():
    """检查目录结构"""
    print("🔍 检查项目目录结构...")
    
    required_dirs = [
        ("suziren", "主包目录"),
        ("suziren/models", "模型模块"),
        ("suziren/core", "核心模块"),
        ("suziren/services", "服务模块"),
        ("suziren/api", "API模块"),
        ("suziren/api/routes", "路由模块"),
        ("suziren/ui", "UI模块"),
        ("suziren/utils", "工具模块"),
        ("suziren/config", "配置模块"),
        ("scripts", "脚本目录"),
        ("tests", "测试目录"),
        ("examples", "示例目录"),
        ("docs", "文档目录"),
    ]
    
    passed = 0
    for dir_path, description in required_dirs:
        if check_file_exists(dir_path, description):
            passed += 1
    
    print(f"📊 目录检查: {passed}/{len(required_dirs)} 通过")
    return passed == len(required_dirs)

def check_key_files():
    """检查关键文件"""
    print("\n🔍 检查关键文件...")
    
    required_files = [
        ("main.py", "主入口文件"),
        ("suziren/__init__.py", "主包初始化"),
        ("suziren/config/__init__.py", "配置包初始化"),
        ("suziren/config/settings.py", "基础配置"),
        ("suziren/config/models.py", "模型配置"),
        ("suziren/models/__init__.py", "模型包初始化"),
        ("suziren/models/kws.py", "KWS模型"),
        ("suziren/models/asr.py", "ASR模型"),
        ("suziren/models/denoise.py", "降噪模型"),
        ("suziren/models/tts.py", "TTS模型"),
        ("suziren/core/__init__.py", "核心包初始化"),
        ("suziren/core/system.py", "系统核心"),
        ("suziren/services/__init__.py", "服务包初始化"),
        ("suziren/services/audio.py", "音频服务"),
        ("suziren/services/interaction.py", "交互服务"),
        ("suziren/services/websocket.py", "WebSocket服务"),
        ("suziren/api/__init__.py", "API包初始化"),
        ("suziren/api/app.py", "FastAPI应用"),
        ("suziren/api/routes/__init__.py", "路由包初始化"),
        ("suziren/api/routes/websocket.py", "WebSocket路由"),
        ("suziren/api/routes/http.py", "HTTP路由"),
        ("suziren/ui/__init__.py", "UI包初始化"),
        ("suziren/ui/gradio_app.py", "Gradio应用"),
        ("suziren/utils/__init__.py", "工具包初始化"),
        ("suziren/utils/logger.py", "日志工具"),
    ]
    
    passed = 0
    for file_path, description in required_files:
        if check_file_exists(file_path, description):
            passed += 1
    
    print(f"📊 文件检查: {passed}/{len(required_files)} 通过")
    return passed == len(required_files)

def check_old_files_removed():
    """检查旧文件是否已移除"""
    print("\n🔍 检查旧文件清理...")
    
    old_paths = [
        ("src", "旧src目录"),
        ("config.py", "旧配置文件"),
    ]
    
    cleaned = 0
    for old_path, description in old_paths:
        if not os.path.exists(old_path):
            print(f"  ✅ {description}: 已清理")
            cleaned += 1
        else:
            print(f"  ⚠️  {description}: 仍存在 ({old_path})")
    
    # 检查备份文件
    if os.path.exists("config.py.old"):
        print(f"  ✅ 配置备份: config.py.old 已保留")
        cleaned += 1
    
    print(f"📊 清理检查: {cleaned}/{len(old_paths)+1} 完成")
    return cleaned >= len(old_paths)

def check_syntax():
    """检查Python语法"""
    print("\n🔍 检查Python语法...")
    
    python_files = []
    for root, dirs, files in os.walk("suziren"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    # 添加主文件
    python_files.extend(["main.py"])
    
    passed = 0
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            compile(code, file_path, 'exec')
            print(f"  ✅ {file_path}")
            passed += 1
        except SyntaxError as e:
            print(f"  ❌ {file_path}: 语法错误 - {e}")
        except Exception as e:
            print(f"  ⚠️  {file_path}: {e}")
    
    print(f"📊 语法检查: {passed}/{len(python_files)} 通过")
    return passed == len(python_files)

def check_model_paths():
    """检查模型路径配置"""
    print("\n🔍 检查模型路径配置...")
    
    model_base = "/pic/suziren/models"
    expected_models = [
        "asr/iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "kws/iic/speech_charctc_kws_phone-xiaoyun", 
        "denoise/iic/speech_zipenhancer_ans_multiloss_16k_base",
        "llm/Qwen/Qwen3-8B",
        "tts/iic/CosyVoice2-0.5B"
    ]
    
    print(f"  📁 模型基础路径: {model_base}")
    
    available = 0
    for model_path in expected_models:
        full_path = os.path.join(model_base, model_path)
        if os.path.exists(full_path):
            print(f"  ✅ {model_path}")
            available += 1
        else:
            print(f"  ❌ {model_path} (路径不存在)")
    
    print(f"📊 模型检查: {available}/{len(expected_models)} 可用")
    return available > 0  # 至少有一个模型可用

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Suziren 项目结构验证")
    print("=" * 60)
    
    checks = [
        ("目录结构", check_directory_structure),
        ("关键文件", check_key_files),
        ("旧文件清理", check_old_files_removed),
        ("Python语法", check_syntax),
        ("模型路径", check_model_paths),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            if check_func():
                passed += 1
                print(f"✅ {check_name} 检查通过")
            else:
                print(f"⚠️  {check_name} 检查部分通过")
        except Exception as e:
            print(f"❌ {check_name} 检查失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 验证结果汇总:")
    print("=" * 60)
    print(f"✅ 通过: {passed}")
    print(f"⚠️  部分通过: {total - passed}")
    print(f"📈 成功率: {passed/total*100:.1f}%")
    
    if passed >= 4:  # 至少4个检查通过
        print("\n🎉 项目结构重构成功！")
        print("\n📋 重构成果:")
        print("  ✅ 优雅的包结构: suziren/")
        print("  ✅ 模块化配置: suziren.config")
        print("  ✅ 分层架构: models/core/services/api/ui")
        print("  ✅ 清晰的命名: 文件名简化")
        print("  ✅ 标准Python包: 符合最佳实践")
        
        print("\n🚀 下一步:")
        print("  1. 配置conda suziren环境")
        print("  2. 安装依赖: pip install -r requirements.txt")
        print("  3. 启动系统: python main.py")
        print("  4. 访问界面: http://localhost:7860")
    else:
        print(f"\n⚠️  项目结构需要进一步完善")
    
    print("=" * 60)
    return passed >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
