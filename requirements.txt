# Suziren语音交互系统依赖包

# 基础框架
fastapi>=0.100.0
uvicorn>=0.20.0
gradio>=4.0.0
websockets>=11.0.0

# 深度学习框架
torch>=2.0.0
transformers>=4.30.0
modelscope>=1.9.0

# 音频处理
librosa>=0.10.0
soundfile>=0.12.0
webrtcvad>=2.0.10
pydub>=0.25.0

# FunASR (语音识别)
funasr>=1.0.0

# 其他工具
numpy>=1.24.0
scipy>=1.10.0
requests>=2.28.0
aiofiles>=23.0.0
python-multipart>=0.0.6

# 日志和配置
loguru>=0.7.0
pydantic>=2.0.0
python-dotenv>=1.0.0

# 开发和测试
pytest>=7.0.0
pytest-asyncio>=0.21.0
