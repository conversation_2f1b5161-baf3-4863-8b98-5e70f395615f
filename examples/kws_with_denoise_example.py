#!/usr/bin/env python3
"""
KWS + 降噪集成示例

演示如何使用 Suziren 的降噪模型 + KWS 模型进行唤醒词检测
"""

import sys
import os
import tempfile

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from models import KWSModel, DenoiseModel
from services import VoiceInteractionService
from utils import setup_logger
from config import MODEL_PATHS


def test_kws_with_denoise():
    """测试KWS + 降噪功能"""
    # 设置日志
    logger = setup_logger("kws_denoise_example")
    
    try:
        # 获取模型配置
        kws_config = MODEL_PATHS["kws"]
        denoise_config = MODEL_PATHS["denoise"]
        
        logger.info("=" * 60)
        logger.info("KWS + 降噪集成测试")
        logger.info("=" * 60)
        
        logger.info(f"KWS模型路径: {kws_config['model_path']}")
        logger.info(f"降噪模型路径: {denoise_config['model_path']}")
        logger.info(f"关键词: {kws_config['keywords']}")
        
        # 检查模型路径
        if not os.path.exists(kws_config["model_path"]):
            logger.error(f"KWS模型路径不存在: {kws_config['model_path']}")
            return False
            
        if not os.path.exists(denoise_config["model_path"]):
            logger.warning(f"降噪模型路径不存在: {denoise_config['model_path']}")
            logger.info("将只使用KWS模型，不进行降噪处理")
            denoise_model = None
        else:
            logger.info("初始化降噪模型...")
            denoise_model = DenoiseModel(
                model_path=denoise_config["model_path"],
                device='cpu'
            )
        
        logger.info("初始化KWS模型...")
        kws_model = KWSModel(
            model_path=kws_config["model_path"],
            keywords=kws_config["keywords"],
            device='cpu'
        )
        
        # 创建语音交互服务
        logger.info("创建语音交互服务...")
        service = VoiceInteractionService(
            kws_model=kws_model,
            denoise_model=denoise_model
        )
        
        # 设置回调函数
        def on_wake_word_detected(detection_info):
            logger.info(f"🎉 检测到唤醒词: {detection_info}")
        
        service.set_wake_word_callback(on_wake_word_detected)
        
        # 测试音频
        test_audio = "https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/KWS/pos_testset/kws_xiaoyunxiaoyun.wav"
        
        logger.info("=" * 40)
        logger.info("开始处理音频...")
        logger.info(f"测试音频: {test_audio}")
        logger.info("=" * 40)
        
        # 处理音频
        result = service.process_audio(test_audio)
        
        # 显示结果
        logger.info("处理结果:")
        logger.info(f"  降噪处理: {'是' if result.get('denoise_applied', False) else '否'}")
        logger.info(f"  检测到唤醒词: {'是' if result['wake_word_detected'] else '否'}")
        logger.info(f"  处理时间: {result['processing_time']:.2f}秒")
        
        if result['wake_word_detected']:
            kws_result = result.get('kws_result', {})
            if 'kws_list' in kws_result and kws_result['kws_list']:
                detection = kws_result['kws_list'][0]
                logger.info(f"  关键词: {detection.get('keyword')}")
                logger.info(f"  置信度: {detection.get('confidence', 0):.3f}")
                logger.info(f"  偏移量: {detection.get('offset', 0):.2f}秒")
                logger.info(f"  长度: {detection.get('length', 0):.2f}秒")
        
        if 'error' in result:
            logger.error(f"处理过程中出现错误: {result['error']}")
            return False
        
        return result['wake_word_detected']
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_denoise_only():
    """单独测试降噪功能"""
    logger = setup_logger("denoise_test")
    
    try:
        denoise_config = MODEL_PATHS["denoise"]
        
        logger.info("=" * 60)
        logger.info("降噪功能单独测试")
        logger.info("=" * 60)
        
        if not os.path.exists(denoise_config["model_path"]):
            logger.error(f"降噪模型路径不存在: {denoise_config['model_path']}")
            return False
        
        logger.info("初始化降噪模型...")
        denoise_model = DenoiseModel(
            model_path=denoise_config["model_path"],
            device='cpu'
        )
        
        # 测试音频（带噪声的音频）
        test_audio = "https://modelscope.oss-cn-beijing.aliyuncs.com/test/audios/speech_with_noise1.wav"
        
        logger.info(f"测试音频: {test_audio}")
        logger.info("开始降噪处理...")
        
        # 执行降噪
        result = denoise_model.denoise(test_audio)
        
        if result['success']:
            logger.info(f"✓ 降噪成功!")
            logger.info(f"  输出文件: {result['output_path']}")
            logger.info(f"  处理时间: {result['inference_time']:.2f}秒")
            
            # 清理临时文件
            if os.path.exists(result['output_path']):
                os.unlink(result['output_path'])
                logger.info("✓ 临时文件已清理")
            
            return True
        else:
            logger.error(f"✗ 降噪失败: {result['error']}")
            return False
            
    except Exception as e:
        logger.error(f"降噪测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🎤 Suziren KWS + 降噪集成测试")
    print()
    
    # 测试1: 降噪功能单独测试
    print("📋 测试1: 降噪功能")
    denoise_success = test_denoise_only()
    print(f"结果: {'✅ 成功' if denoise_success else '❌ 失败'}")
    print()
    
    # 测试2: KWS + 降噪集成测试
    print("📋 测试2: KWS + 降噪集成")
    kws_success = test_kws_with_denoise()
    print(f"结果: {'✅ 成功' if kws_success else '❌ 失败'}")
    print()
    
    # 总结
    overall_success = denoise_success and kws_success
    print("=" * 60)
    print("测试总结:")
    print(f"降噪功能: {'✅ 通过' if denoise_success else '❌ 失败'}")
    print(f"KWS集成: {'✅ 通过' if kws_success else '❌ 失败'}")
    print(f"整体结果: {'🎉 全部通过!' if overall_success else '⚠️ 部分失败'}")
    print("=" * 60)
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
