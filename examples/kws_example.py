#!/usr/bin/env python3
"""
KWS 唤醒词检测示例

演示如何使用 Suziren 的 KWS 模型进行唤醒词检测
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from models import KWSModel
from utils import setup_logger
from config import MODEL_PATHS


def main():
    """主函数"""
    # 设置日志
    logger = setup_logger("kws_example")
    
    try:
        # 获取KWS配置
        kws_config = MODEL_PATHS["kws"]
        model_path = kws_config["model_path"]
        keywords = kws_config["keywords"]
        
        logger.info(f"初始化KWS模型: {model_path}")
        logger.info(f"关键词: {keywords}")
        
        # 创建KWS模型
        kws_model = KWSModel(
            model_path=model_path,
            keywords=keywords,
            device='cpu'
        )
        
        # 测试音频
        test_audio = "https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/KWS/pos_testset/kws_xiaoyunxiaoyun.wav"
        
        logger.info(f"检测音频: {test_audio}")
        
        # 进行检测
        result = kws_model.detect(test_audio)
        
        logger.info("检测结果:")
        logger.info(f"  原始结果: {result}")
        
        # 判断是否检测到唤醒词
        is_detected = kws_model.is_wake_word_detected(result)
        logger.info(f"  是否检测到唤醒词: {is_detected}")
        
        if is_detected:
            detection_info = kws_model.get_detection_info(result)
            logger.info(f"  检测信息: {detection_info}")
            
            print("🎉 唤醒词检测成功！")
            print(f"关键词: {detection_info['keyword']}")
            print(f"置信度: {detection_info['confidence']:.3f}")
            print(f"偏移量: {detection_info['offset']:.2f}秒")
            print(f"长度: {detection_info['length']:.2f}秒")
        else:
            print("❌ 未检测到唤醒词")
        
        return is_detected
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
