#!/usr/bin/env python3
"""
Suziren语音交互系统主配置文件
"""

import os
from pathlib import Path

# 基础路径配置
PROJECT_ROOT = Path(__file__).parent.parent.parent
MODEL_BASE_PATH = "/pic/suziren/models"

# 音频配置
AUDIO_CONFIG = {
    "sample_rate": 16000,
    "channels": 1,
    "bit_depth": 16,
    "chunk_size": 1024,
    "vad_frame_duration": 30,  # ms
    "vad_aggressiveness": 3,   # 0-3, 3最激进
}

# WebSocket配置
WEBSOCKET_CONFIG = {
    "host": "0.0.0.0",
    "port": 8000,
    "max_connections": 10,
    "ping_interval": 30,
    "ping_timeout": 10,
}

# Gradio配置
GRADIO_CONFIG = {
    "host": "0.0.0.0",
    "port": 7860,
    "share": False,
    "debug": True,
}

# 系统配置
SYSTEM_CONFIG = {
    "max_audio_length": 30,  # 最大音频长度(秒)
    "max_conversation_history": 10,  # 最大对话历史条数
    "response_timeout": 30,  # 响应超时时间(秒)
    "model_load_timeout": 300,  # 模型加载超时时间(秒)
}

# GPU配置
GPU_CONFIG = {
    "device": "cuda" if os.environ.get("CUDA_VISIBLE_DEVICES") else "auto",
    "max_memory_per_gpu": "20GB",
    "load_in_8bit": False,
    "load_in_4bit": False,
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
    "rotation": "1 day",
    "retention": "7 days",
    "log_file": "logs/suziren.log"
}

# 高速公路展厅专用配置
EXHIBITION_CONFIG = {
    "system_prompt": """你是湖南高速公路展厅的智能语音助手，名字叫"小云"。你的任务是为参观者提供关于湖南高速公路的信息和服务。

你需要：
1. 用友好、专业的语气回答问题
2. 重点介绍湖南高速公路的发展历程、建设成就、服务设施等
3. 为参观者提供路线规划、服务区信息等实用信息
4. 保持回答简洁明了，适合语音交互
5. 如果遇到不确定的问题，诚实地说明并建议咨询工作人员

请用中文回答所有问题。""",
    
    "welcome_message": "欢迎来到湖南高速公路展厅！我是智能语音助手小云，很高兴为您服务。您可以问我关于湖南高速公路的任何问题。",
    
    "fallback_responses": [
        "抱歉，我没有理解您的问题，您可以换个方式问我。",
        "这个问题我需要进一步了解，建议您咨询现场的工作人员。",
        "让我想想...您能再详细说明一下您想了解什么吗？"
    ]
}

def validate_config():
    """验证配置是否正确"""
    errors = []
    
    # 检查日志目录
    log_dir = Path(LOG_CONFIG["log_file"]).parent
    if not log_dir.exists():
        try:
            log_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            errors.append(f"无法创建日志目录: {e}")
    
    return errors
