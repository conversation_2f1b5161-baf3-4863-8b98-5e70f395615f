"""
Denoise (语音降噪) 模型封装
"""

import os
import time
import tempfile
from typing import Union, Dict, Optional
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks


class DenoiseModel:
    """语音降噪模型"""
    
    def __init__(self, model_path: str, device: str = 'cpu'):
        """
        初始化降噪模型
        
        Args:
            model_path: 模型路径
            device: 设备类型 ('cpu' 或 'cuda')
        """
        self.model_path = model_path
        self.device = device
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
        
        print(f"正在加载降噪模型: {self.model_path}")
        start_time = time.time()
        
        self.model = pipeline(
            task=Tasks.acoustic_noise_suppression,
            model=self.model_path
        )
        
        load_time = time.time() - start_time
        print(f"✓ 降噪模型加载完成，耗时: {load_time:.2f}秒")
    
    def denoise(self, audio_input: Union[str, bytes], output_path: Optional[str] = None) -> Dict:
        """
        音频降噪

        Args:
            audio_input: 音频输入，可以是文件路径或音频数据
            output_path: 输出文件路径，如果为None则使用临时文件

        Returns:
            包含降噪结果的字典
        """
        if self.model is None:
            raise RuntimeError("模型未加载")

        start_time = time.time()

        # 如果没有指定输出路径，使用临时文件
        if output_path is None:
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            output_path = temp_file.name
            temp_file.close()

        try:
            # 执行降噪
            result = self.model(audio_input, output_path=output_path)

            inference_time = time.time() - start_time

            # 返回结果字典
            return {
                'output_path': output_path,
                'inference_time': inference_time,
                'success': True,
                'original_input': audio_input
            }

        except Exception as e:
            inference_time = time.time() - start_time
            return {
                'output_path': None,
                'inference_time': inference_time,
                'success': False,
                'error': str(e),
                'original_input': audio_input
            }

    def denoise_for_kws(self, audio_input: Union[str, bytes]) -> str:
        """
        为KWS处理进行音频降噪，返回降噪后的音频文件路径

        Args:
            audio_input: 音频输入

        Returns:
            降噪后的音频文件路径
        """
        result = self.denoise(audio_input)

        if result['success']:
            print(f"✓ 音频降噪完成，耗时: {result['inference_time']:.2f}秒")
            return result['output_path']
        else:
            print(f"✗ 音频降噪失败: {result['error']}")
            # 降噪失败时返回原始音频
            return audio_input if isinstance(audio_input, str) else None
