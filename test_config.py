#!/usr/bin/env python3
"""
配置导入测试脚本
"""

import sys
import os

print("🔍 配置导入诊断:")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python路径: {sys.path[:3]}...")
print()

# 测试1: 检查config目录
print("1. 检查config目录:")
if os.path.exists("config"):
    print("  ✅ config目录存在")
    files = os.listdir("config")
    print(f"  📁 文件列表: {files}")
else:
    print("  ❌ config目录不存在")

# 测试2: 检查__init__.py
print("\n2. 检查__init__.py:")
if os.path.exists("config/__init__.py"):
    print("  ✅ config/__init__.py存在")
else:
    print("  ❌ config/__init__.py不存在")

# 测试3: 尝试导入config模块
print("\n3. 尝试导入config模块:")
try:
    import config
    print("  ✅ config模块导入成功")
    print(f"  📍 模块位置: {config.__file__}")
except Exception as e:
    print(f"  ❌ config模块导入失败: {e}")

# 测试4: 尝试导入具体配置
print("\n4. 尝试导入具体配置:")
try:
    from config.settings import AUDIO_CONFIG
    print("  ✅ settings导入成功")
    print(f"  🎵 音频采样率: {AUDIO_CONFIG['sample_rate']}")
except Exception as e:
    print(f"  ❌ settings导入失败: {e}")

try:
    from config.models import MODEL_PATHS
    print("  ✅ models导入成功")
    print(f"  🤖 模型数量: {len(MODEL_PATHS)}")
except Exception as e:
    print(f"  ❌ models导入失败: {e}")

# 测试5: 尝试从config包导入
print("\n5. 尝试从config包导入:")
try:
    from config import MODEL_PATHS, AUDIO_CONFIG
    print("  ✅ 从config包导入成功")
    print(f"  🎵 音频采样率: {AUDIO_CONFIG['sample_rate']}")
    print(f"  🤖 模型数量: {len(MODEL_PATHS)}")
except Exception as e:
    print(f"  ❌ 从config包导入失败: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 诊断完成!")
