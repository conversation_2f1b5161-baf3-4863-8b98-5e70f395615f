#!/usr/bin/env python3
"""
主入口验证脚本

快速验证新重构的main.py是否可以正常启动
"""

import sys
import os
import time
import subprocess
import signal
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'fastapi',
        'uvicorn', 
        'gradio',
        'websockets',
        'modelscope',
        'funasr'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing)}")
        print("请运行: pip install " + " ".join(missing))
        return False
    
    print("✅ 所有依赖包检查通过")
    return True


def check_imports():
    """检查模块导入"""
    print("\n🔍 检查模块导入...")
    
    try:
        # 检查主要模块
        from src.suziren.models import KWSModel, ASRModel, DenoiseModel, TTSModel
        print("  ✅ 模型模块")
        
        from src.suziren.services import AudioProcessor, VoiceInteractionService, WebSocketAudioHandler
        print("  ✅ 服务模块")
        
        from src.suziren.core import AudioInteractionSystem
        print("  ✅ 核心模块")
        
        from src.suziren.ui import GradioInterface
        print("  ✅ UI模块")
        
        from src.suziren.utils import setup_logger
        print("  ✅ 工具模块")
        
        print("✅ 所有模块导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def check_config():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    try:
        from config import MODEL_PATHS, AUDIO_CONFIG, WEBSOCKET_CONFIG, GRADIO_CONFIG
        
        # 检查必要配置项
        assert 'kws' in MODEL_PATHS, "缺少KWS模型配置"
        assert 'sample_rate' in AUDIO_CONFIG, "缺少音频采样率配置"
        assert 'host' in WEBSOCKET_CONFIG, "缺少WebSocket主机配置"
        assert 'port' in WEBSOCKET_CONFIG, "缺少WebSocket端口配置"
        assert 'host' in GRADIO_CONFIG, "缺少Gradio主机配置"
        assert 'port' in GRADIO_CONFIG, "缺少Gradio端口配置"
        
        print("  ✅ 配置结构完整")
        
        # 检查端口冲突
        ws_port = WEBSOCKET_CONFIG['port']
        gradio_port = GRADIO_CONFIG['port']
        
        if ws_port == gradio_port:
            print(f"  ⚠️  端口冲突: WebSocket和Gradio都使用端口 {ws_port}")
            return False
        
        print(f"  ✅ 端口配置: WebSocket={ws_port}, Gradio={gradio_port}")
        print("✅ 配置文件检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False


def check_main_syntax():
    """检查main.py语法"""
    print("\n🔍 检查main.py语法...")
    
    try:
        # 尝试编译main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'main.py', 'exec')
        print("✅ main.py语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ main.py语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ main.py检查失败: {e}")
        return False


def test_main_import():
    """测试main.py导入"""
    print("\n🔍 测试main.py导入...")
    
    try:
        # 模拟配置验证通过
        import unittest.mock
        with unittest.mock.patch('config.validate_config', return_value=[]):
            from main import SuzirenApplication, main
            print("  ✅ SuzirenApplication类")
            print("  ✅ main函数")
        
        print("✅ main.py导入成功")
        return True
        
    except Exception as e:
        print(f"❌ main.py导入失败: {e}")
        return False


def test_dry_run():
    """测试干运行（不启动服务器）"""
    print("\n🔍 测试应用程序初始化...")
    
    try:
        import unittest.mock
        
        # 模拟所有外部依赖
        with unittest.mock.patch('config.validate_config', return_value=[]), \
             unittest.mock.patch('uvicorn.run'), \
             unittest.mock.patch('threading.Thread'):
            
            from main import SuzirenApplication
            
            app = SuzirenApplication()
            assert app.logger is not None
            
            # 测试FastAPI应用创建
            with unittest.mock.patch.object(app, 'initialize_system'):
                fastapi_app = app.create_fastapi_app()
                assert fastapi_app is not None
                
                # 设置路由
                app.setup_websocket_routes(fastapi_app)
                app.setup_http_routes(fastapi_app)
                
                print("  ✅ 应用程序初始化")
                print("  ✅ FastAPI应用创建")
                print("  ✅ 路由设置")
        
        print("✅ 干运行测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 干运行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Suziren 主入口验证")
    print("=" * 60)
    
    # 切换到项目根目录
    os.chdir(project_root)
    
    checks = [
        ("依赖包检查", check_dependencies),
        ("模块导入检查", check_imports),
        ("配置文件检查", check_config),
        ("语法检查", check_main_syntax),
        ("导入测试", test_main_import),
        ("干运行测试", test_dry_run),
    ]
    
    passed = 0
    failed = 0
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            if check_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {check_name}异常: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("📊 验证结果汇总:")
    print("=" * 60)
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有验证通过！main.py已准备就绪。")
        print("\n🚀 启动命令:")
        print("   python main.py")
        print("\n🌐 访问地址:")
        print("   - Gradio控制台: http://localhost:7860")
        print("   - WebSocket音频: ws://localhost:8000/audio")
        print("   - 系统状态: http://localhost:8000/status")
        
        # 询问是否启动
        try:
            response = input("\n❓ 是否现在启动系统？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                print("\n🚀 启动系统...")
                os.system("python main.py")
        except KeyboardInterrupt:
            print("\n👋 已取消启动")
    else:
        print(f"\n⚠️  有 {failed} 个验证失败，请先解决问题。")
        print("\n🔧 建议:")
        print("1. 检查依赖包安装: pip install -r requirements.txt")
        print("2. 检查模型文件: python scripts/download_models.py")
        print("3. 运行完整测试: python tests/test_main_integration.py")
    
    print("=" * 60)
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
