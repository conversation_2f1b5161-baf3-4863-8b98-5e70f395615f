"""
WebSocket 音频流处理器
"""

import asyncio
import json
import logging
from typing import Dict, Any
from fastapi import WebSocket, WebSocketDisconnect
from core import AudioInteractionSystem


class WebSocketAudioHandler:
    """WebSocket音频流处理器"""
    
    def __init__(self, system: AudioInteractionSystem):
        """
        初始化WebSocket处理器
        
        Args:
            system: 音频交互系统实例
        """
        self.system = system
        self.logger = logging.getLogger(__name__)
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """
        建立WebSocket连接
        
        Args:
            websocket: WebSocket连接
            client_id: 客户端ID
        """
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.logger.info(f"客户端 {client_id} 已连接")
        
        # 发送连接确认消息
        await self.send_message(client_id, {
            "type": "connection_established",
            "client_id": client_id,
            "system_status": self.system.get_status()
        })
    
    def disconnect(self, client_id: str):
        """
        断开WebSocket连接
        
        Args:
            client_id: 客户端ID
        """
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            self.logger.info(f"客户端 {client_id} 已断开连接")
    
    async def send_message(self, client_id: str, message: Dict[str, Any]):
        """
        发送消息给客户端
        
        Args:
            client_id: 客户端ID
            message: 消息内容
        """
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                self.logger.error(f"发送消息失败: {e}")
                self.disconnect(client_id)
    
    async def broadcast_message(self, message: Dict[str, Any]):
        """
        广播消息给所有客户端
        
        Args:
            message: 消息内容
        """
        for client_id in list(self.active_connections.keys()):
            await self.send_message(client_id, message)
    
    async def handle_audio_stream(self, websocket: WebSocket, client_id: str):
        """
        处理音频流
        
        Args:
            websocket: WebSocket连接
            client_id: 客户端ID
        """
        try:
            while True:
                # 接收音频数据
                data = await websocket.receive_bytes()
                
                # 处理音频数据
                result = await self.system.process_audio_chunk(data)
                
                # 发送处理结果
                await self.send_message(client_id, {
                    "type": "audio_processed",
                    "result": result,
                    "timestamp": result.get('timestamp')
                })
                
                # 如果检测到唤醒词，发送特殊消息
                if result.get('wake_word_detected'):
                    await self.send_message(client_id, {
                        "type": "wake_word_detected",
                        "detection_info": result.get('kws_result'),
                        "timestamp": result.get('timestamp')
                    })
                
                # 如果有语音识别结果，发送文本
                if result.get('speech_text'):
                    await self.send_message(client_id, {
                        "type": "speech_recognized",
                        "text": result['speech_text'],
                        "timestamp": result.get('timestamp')
                    })
        
        except WebSocketDisconnect:
            self.disconnect(client_id)
        except Exception as e:
            self.logger.error(f"处理音频流时出错: {e}")
            await self.send_message(client_id, {
                "type": "error",
                "message": str(e)
            })
    
    async def handle_control_message(self, websocket: WebSocket, client_id: str, message: Dict[str, Any]):
        """
        处理控制消息
        
        Args:
            websocket: WebSocket连接
            client_id: 客户端ID
            message: 控制消息
        """
        try:
            msg_type = message.get('type')
            
            if msg_type == 'start_listening':
                self.system.start_listening()
                await self.send_message(client_id, {
                    "type": "listening_started",
                    "status": self.system.get_status()
                })
            
            elif msg_type == 'stop_listening':
                self.system.stop_listening()
                await self.send_message(client_id, {
                    "type": "listening_stopped",
                    "status": self.system.get_status()
                })
            
            elif msg_type == 'get_status':
                await self.send_message(client_id, {
                    "type": "status_response",
                    "status": self.system.get_status()
                })
            
            elif msg_type == 'generate_response':
                text = message.get('text', '')
                if text:
                    audio_path = await self.system.generate_response(text)
                    await self.send_message(client_id, {
                        "type": "response_generated",
                        "text": text,
                        "audio_path": audio_path
                    })
            
            else:
                await self.send_message(client_id, {
                    "type": "error",
                    "message": f"未知的消息类型: {msg_type}"
                })
        
        except Exception as e:
            self.logger.error(f"处理控制消息时出错: {e}")
            await self.send_message(client_id, {
                "type": "error",
                "message": str(e)
            })
    
    def get_connection_count(self) -> int:
        """获取活跃连接数"""
        return len(self.active_connections)
