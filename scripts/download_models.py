#!/usr/bin/env python3
"""
Suziren语音交互系统 - 模型下载脚本
支持断点续传和错误重试
"""

import os
import sys
import time
import shutil
from pathlib import Path
from modelscope import snapshot_download
from modelscope.hub.errors import FileIntegrityError

# 模型配置
MODELS = {
    "asr": {
        "name": "iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "cache_dir": "/pic/suziren/models/asr",
        "description": "ASR模型 (Paraformer)"
    },
    "kws": {
        "name": "iic/speech_charctc_kws_phone-xiaoyun",
        "cache_dir": "/pic/suziren/models/kws",
        "description": "唤醒词检测模型 (小云小云)"
    },
    "denoise": {
        "name": "iic/speech_zipenhancer_ans_multiloss_16k_base",
        "cache_dir": "/pic/suziren/models/denoise",
        "description": "降噪模型 (ZipEnhancer)"
    },
    "llm": {
        "name": "Qwen/Qwen3-8B",
        "cache_dir": "/pic/suziren/models/llm",
        "description": "LLM模型 (Qwen3-8B)"
    },
    "tts": {
        "name": "iic/CosyVoice2-0.5B",
        "cache_dir": "/pic/suziren/models/tts",
        "description": "TTS模型 (CosyVoice2-0.5B)"
    }
}

def clean_temp_files(cache_dir):
    """清理临时文件"""
    temp_dir = os.path.join(cache_dir, "._____temp")
    if os.path.exists(temp_dir):
        print(f"清理临时文件: {temp_dir}")
        shutil.rmtree(temp_dir)

def download_model_with_retry(model_name, model_config, max_retries=3):
    """带重试机制的模型下载"""
    cache_dir = model_config["cache_dir"]
    model_id = model_config["name"]
    description = model_config["description"]
    
    print(f"\n{'='*60}")
    print(f"开始下载: {description}")
    print(f"模型ID: {model_id}")
    print(f"存储路径: {cache_dir}")
    print(f"{'='*60}")
    
    # 设置环境变量
    os.environ['MODELSCOPE_CACHE'] = cache_dir
    
    for attempt in range(max_retries):
        try:
            print(f"\n尝试 {attempt + 1}/{max_retries}...")
            
            # 清理之前的临时文件
            clean_temp_files(cache_dir)
            
            # 下载模型
            result = snapshot_download(
                model_id,
                cache_dir=cache_dir,
                revision='master'
            )
            
            print(f"✓ {description} 下载完成!")
            print(f"模型路径: {result}")
            return True
            
        except FileIntegrityError as e:
            print(f"✗ 文件完整性检查失败: {e}")
            if attempt < max_retries - 1:
                print("清理损坏的文件，准备重试...")
                clean_temp_files(cache_dir)
                time.sleep(5)  # 等待5秒后重试
            else:
                print(f"✗ {description} 下载失败，已达到最大重试次数")
                return False
                
        except Exception as e:
            print(f"✗ 下载出错: {e}")
            if attempt < max_retries - 1:
                print("准备重试...")
                clean_temp_files(cache_dir)
                time.sleep(5)
            else:
                print(f"✗ {description} 下载失败，已达到最大重试次数")
                return False
    
    return False

def check_model_exists(model_config):
    """检查模型是否已存在"""
    cache_dir = model_config["cache_dir"]
    model_id = model_config["name"]
    
    # 检查模型目录是否存在且包含文件
    model_path = os.path.join(cache_dir, model_id.replace("/", "/"))
    if os.path.exists(model_path):
        files = list(Path(model_path).rglob("*"))
        if len(files) > 5:  # 如果有足够多的文件，认为模型已存在
            return True
    return False

def main():
    """主函数"""
    print("Suziren语音交互系统 - 模型下载工具")
    print("=" * 60)
    
    # 检查参数
    if len(sys.argv) > 1:
        # 下载指定模型
        model_names = sys.argv[1:]
        models_to_download = {name: MODELS[name] for name in model_names if name in MODELS}
        if not models_to_download:
            print("错误: 指定的模型不存在")
            print(f"可用模型: {', '.join(MODELS.keys())}")
            return False
    else:
        # 下载所有模型
        models_to_download = MODELS
    
    print(f"准备下载 {len(models_to_download)} 个模型...")
    
    # 创建目录
    for model_config in models_to_download.values():
        os.makedirs(model_config["cache_dir"], exist_ok=True)
    
    # 下载模型
    success_count = 0
    total_count = len(models_to_download)
    
    for model_name, model_config in models_to_download.items():
        # 检查模型是否已存在
        if check_model_exists(model_config):
            print(f"\n✓ {model_config['description']} 已存在，跳过下载")
            success_count += 1
            continue
        
        # 下载模型
        if download_model_with_retry(model_name, model_config):
            success_count += 1
        else:
            print(f"\n❌ {model_config['description']} 下载失败")
    
    # 总结
    print(f"\n{'='*60}")
    print("下载完成!")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有模型下载成功!")
        return True
    else:
        print("⚠️  部分模型下载失败，请检查网络连接后重试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
