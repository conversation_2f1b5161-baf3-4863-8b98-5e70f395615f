#!/usr/bin/env python3
"""
Suziren 语音交互系统主入口

专为湖南高速公路展厅设计的模块化语音交互系统
支持实时音频流处理、唤醒词检测、语音识别和TTS响应

架构特点:
- 模块化管道: 降噪 → 唤醒检测 → VAD → ASR → TTS
- 异步处理: FastAPI + WebSocket + 异步任务调度
- 多接口: Gradio控制台 + WebSocket API
- 配置驱动: 所有参数通过配置模块加载
"""

import asyncio
import logging
import os
import sys
import threading
import time

import uvicorn

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from config import (
    MODEL_PATHS, AUDIO_CONFIG, WEBSOCKET_CONFIG,
    GRADIO_CONFIG, SYSTEM_CONFIG, LOG_CONFIG,
    validate_config, validate_model_paths
)
from core import AudioInteractionSystem
from services import WebSocketAudioHandler
from ui import GradioInterface
from utils import setup_logger
from api import create_app


class SuzirenApplication:
    """Suziren应用程序主类"""

    def __init__(self):
        """初始化应用程序"""
        self.logger = setup_logger("suziren_main")
        self.system: AudioInteractionSystem = None
        self.websocket_handler: WebSocketAudioHandler = None
        self.gradio_interface: GradioInterface = None
        self.app = None

        # 验证配置
        self._validate_configuration()

    def _validate_configuration(self):
        """验证系统配置"""
        self.logger.info("验证系统配置...")

        # 验证基础配置
        config_errors = validate_config()
        if config_errors:
            self.logger.warning("基础配置问题:")
            for error in config_errors:
                self.logger.warning(f"  - {error}")

        # 验证模型路径
        model_errors = validate_model_paths()
        if model_errors:
            self.logger.warning("模型路径问题:")
            for error in model_errors:
                self.logger.warning(f"  - {error}")
            self.logger.warning("部分模型可能无法加载，但系统仍可启动")

        self.logger.info("✓ 配置验证完成")

    def create_application(self):
        """创建应用程序"""
        self.logger.info("创建FastAPI应用...")

        # 使用新的API模块创建应用
        self.app, self.system, self.websocket_handler = create_app()

        # 设置系统回调
        if self.system:
            self.system.on_wake_word_detected = self._on_wake_word_detected
            self.system.on_speech_recognized = self._on_speech_recognized
            self.system.on_response_generated = self._on_response_generated

        self.logger.info("✓ FastAPI应用创建完成")
        return self.app



    async def _on_wake_word_detected(self, detection_info: Dict):
        """唤醒词检测回调"""
        self.logger.info(f"🎤 检测到唤醒词: {detection_info}")

        # 广播唤醒词检测事件
        if self.websocket_handler:
            await self.websocket_handler.broadcast_message({
                "type": "wake_word_detected",
                "detection_info": detection_info,
                "timestamp": time.time()
            })

    async def _on_speech_recognized(self, text: str):
        """语音识别回调"""
        self.logger.info(f"🗣️ 识别到语音: {text}")

        # 广播语音识别结果
        if self.websocket_handler:
            await self.websocket_handler.broadcast_message({
                "type": "speech_recognized",
                "text": text,
                "timestamp": time.time()
            })

    async def _on_response_generated(self, text: str, audio_path: str):
        """响应生成回调"""
        self.logger.info(f"🔊 生成响应: {text} -> {audio_path}")

        # 广播响应生成事件
        if self.websocket_handler:
            await self.websocket_handler.broadcast_message({
                "type": "response_generated",
                "text": text,
                "audio_path": audio_path,
                "timestamp": time.time()
            })

    def start_gradio_interface(self):
        """在单独线程中启动Gradio界面"""
        def run_gradio():
            try:
                # 等待系统初始化完成
                while not self.system or not self.system.is_initialized:
                    time.sleep(1)

                self.gradio_interface = GradioInterface(self.system, GRADIO_CONFIG)
                self.gradio_interface.launch()

            except Exception as e:
                self.logger.error(f"Gradio界面启动失败: {e}")

        gradio_thread = threading.Thread(target=run_gradio, daemon=True)
        gradio_thread.start()
        self.logger.info(f"✓ Gradio界面将在 http://localhost:{GRADIO_CONFIG['port']} 启动")

    def run(self):
        """运行应用程序"""
        self.logger.info("=" * 60)
        self.logger.info("🚀 启动 Suziren 语音交互系统 v2.0")
        self.logger.info("=" * 60)

        try:
            # 创建应用
            self.create_application()

            # 启动Gradio界面（在单独线程中）
            self.start_gradio_interface()

            # 启动FastAPI服务器
            self.logger.info(f"🌐 启动WebSocket服务器: {WEBSOCKET_CONFIG['host']}:{WEBSOCKET_CONFIG['port']}")

            uvicorn.run(
                self.app,
                host=WEBSOCKET_CONFIG["host"],
                port=WEBSOCKET_CONFIG["port"],
                log_level="info",
                access_log=True
            )

        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在关闭...")
        except Exception as e:
            self.logger.error(f"应用程序运行失败: {e}")
            raise
        finally:
            self.logger.info("✓ 应用程序已关闭")


def main():
    """主函数"""
    try:
        app = SuzirenApplication()
        app.run()
    except Exception as e:
        logging.error(f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
