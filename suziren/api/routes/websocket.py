"""
WebSocket路由
"""

import time
import logging
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from ...services import WebSocketAudioHandler

logger = logging.getLogger(__name__)


def setup_websocket_routes(app: FastAPI, websocket_handler: WebSocketAudioHandler):
    """设置WebSocket路由"""
    
    @app.websocket("/audio")
    async def audio_stream_endpoint(websocket: WebSocket):
        """音频流WebSocket端点"""
        client_id = f"client_{int(time.time() * 1000)}"
        
        try:
            await websocket_handler.connect(websocket, client_id)
            await websocket_handler.handle_audio_stream(websocket, client_id)
        except WebSocketDisconnect:
            websocket_handler.disconnect(client_id)
        except Exception as e:
            logger.error(f"WebSocket错误: {e}")
            websocket_handler.disconnect(client_id)
    
    @app.websocket("/control")
    async def control_endpoint(websocket: WebSocket):
        """控制WebSocket端点"""
        client_id = f"control_{int(time.time() * 1000)}"
        
        try:
            await websocket_handler.connect(websocket, client_id)
            
            while True:
                # 接收控制消息
                message = await websocket.receive_json()
                await websocket_handler.handle_control_message(
                    websocket, client_id, message
                )
        
        except WebSocketDisconnect:
            websocket_handler.disconnect(client_id)
        except Exception as e:
            logger.error(f"控制WebSocket错误: {e}")
            websocket_handler.disconnect(client_id)
