"""
FastAPI应用创建
"""

import os
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from core import AudioInteractionSystem
from services import WebSocketAudioHandler
from config import MODEL_PATHS, AUDIO_CONFIG, SYSTEM_CONFIG
from api.routes import setup_websocket_routes, setup_http_routes


def create_app() -> tuple[FastAPI, AudioInteractionSystem, WebSocketAudioHandler]:
    """创建FastAPI应用"""
    
    # 全局变量存储系统实例
    system = None
    websocket_handler = None
    
    @asynccontextmanager
    async def lifespan(app: FastAPI):
        """应用生命周期管理"""
        nonlocal system, websocket_handler
        
        # 启动时初始化
        system_config = {
            'models': MODEL_PATHS,
            'audio': AUDIO_CONFIG,
            'system': SYSTEM_CONFIG,
            'device': 'cpu'  # 可以从环境变量读取
        }
        
        system = AudioInteractionSystem(system_config)
        await system.initialize()
        
        websocket_handler = WebSocketAudioHandler(system)
        
        yield
        
        # 关闭时清理
        pass
    
    # 创建FastAPI应用
    app = FastAPI(
        title="Suziren 语音交互系统",
        description="湖南高速公路展厅专用语音交互API",
        version="2.0.0",
        lifespan=lifespan
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 静态文件服务（如果需要）
    if os.path.exists("static"):
        app.mount("/static", StaticFiles(directory="static"), name="static")
    
    # 设置路由（延迟到系统初始化后）
    @app.on_event("startup")
    async def setup_routes():
        nonlocal system, websocket_handler
        # 等待系统初始化完成
        while not system or not websocket_handler:
            import asyncio
            await asyncio.sleep(0.1)
        
        setup_websocket_routes(app, websocket_handler)
        setup_http_routes(app, system, websocket_handler)
    
    return app, system, websocket_handler
