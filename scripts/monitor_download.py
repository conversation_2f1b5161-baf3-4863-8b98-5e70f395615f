#!/usr/bin/env python3
"""
模型下载进度监控脚本
"""

import os
import time
import subprocess
from pathlib import Path

def get_directory_size(path):
    """获取目录大小"""
    try:
        result = subprocess.run(['du', '-sh', path], capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.strip().split('\t')[0]
        return "0B"
    except:
        return "0B"

def check_download_progress():
    """检查下载进度"""
    model_dirs = {
        "ASR": "/pic/suziren/models/asr",
        "KWS": "/pic/suziren/models/kws", 
        "Denoise": "/pic/suziren/models/denoise",
        "LLM": "/pic/suziren/models/llm",
        "TTS": "/pic/suziren/models/tts"
    }
    
    print(f"{'='*60}")
    print(f"模型下载进度监控 - {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    total_size = 0
    for model_name, model_path in model_dirs.items():
        if os.path.exists(model_path):
            size = get_directory_size(model_path)
            print(f"{model_name:10}: {size:>8}")
            
            # 检查是否有临时文件（表示正在下载）
            temp_dir = os.path.join(model_path, "._____temp")
            if os.path.exists(temp_dir):
                temp_size = get_directory_size(temp_dir)
                print(f"{'':10}  (临时文件: {temp_size})")
        else:
            print(f"{model_name:10}: {'未开始':>8}")
    
    # 总大小
    total_size = get_directory_size("/pic/suziren/models")
    print(f"{'='*60}")
    print(f"{'总计':10}: {total_size:>8}")
    print(f"{'='*60}")

def main():
    """主函数"""
    try:
        while True:
            check_download_progress()
            print("\n按 Ctrl+C 退出监控\n")
            time.sleep(30)  # 每30秒检查一次
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == "__main__":
    main()
